{"name": "airchat-workspace", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "pnpm -r --parallel dev", "build": "pnpm -r build", "test": "pnpm -r test", "test:e2e": "pnpm -C packages/airchat-client test:e2e", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "type-check": "pnpm -r type-check", "format": "prettier --write **/*.{ts,tsx,js,jsx,md,json}", "clean": "pnpm -r exec rm -rf dist node_modules", "deploy:signaling": "pnpm -C packages/airchat-signaling deploy", "deploy:client": "pnpm -C packages/airchat-client build && echo 'Deploy client to your hosting provider'", "prepare": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "prettier": "^3.0.0", "typescript": "^5.0.2"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{md,json}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}