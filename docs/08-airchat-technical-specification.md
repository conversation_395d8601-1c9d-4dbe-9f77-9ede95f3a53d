# 🔧 AirChat Technical Specification

## Architecture Overview

AirChat combines secure messaging with universal file sharing using WebRTC P2P technology. This document details the complete technical implementation.

---

## 🏗️ System Architecture

### High-Level Components
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AirChat UI    │    │  P2P Connection  │    │ Signaling Server│
│                 │    │    Manager       │    │ (Cloudflare)    │
│ • Chat Interface│◄──►│                  │◄──►│                 │
│ • File Manager  │    │ • Message Queue  │    │ • Room Manager  │
│ • Contact List  │    │ • File Transfer  │    │ • Peer Discovery│
│ • Settings      │    │ • Device Sync    │    │ • WebRTC Relay  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌──────────────────┐
                    │   Local Storage  │
                    │                  │
                    │ • Messages       │
                    │ • Files          │
                    │ • Contacts       │
                    │ • Device Keys    │
                    └──────────────────┘
```

---

## 📱 Application Layers

### 1. **Presentation Layer** (`src/components/`)
```typescript
// Main application component
export interface AirChatUIProps {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: boolean;
}

export const AirChatUI: React.FC<AirChatUIProps> = ({
  theme,
  language,
  notifications
}) => {
  return (
    <div className="airchat-app">
      <Sidebar>
        <ConversationList />
        <ContactManager />
        <FileManager />
      </Sidebar>
      
      <MainContent>
        <ChatInterface />
        <FileTransferPanel />
      </MainContent>
      
      <StatusBar>
        <ConnectionStatus />
        <TransferProgress />
      </StatusBar>
    </div>
  );
};
```

### 2. **Business Logic Layer** (`src/core/`)
```typescript
// Core application orchestrator
export class AirChatApp {
  private messaging: SecureMessaging;
  private fileTransfer: UniversalFileTransfer;
  private contacts: ContactManager;
  private deviceSync: DeviceSync;
  private storage: LocalStorage;

  constructor(config: AirChatConfig) {
    this.messaging = new SecureMessaging(config.encryption);
    this.fileTransfer = new UniversalFileTransfer(config.transfer);
    this.contacts = new ContactManager(config.contacts);
    this.deviceSync = new DeviceSync(config.sync);
    this.storage = new LocalStorage(config.storage);
  }

  // Core messaging functionality
  async sendMessage(to: string, content: MessageContent): Promise<void> {
    const message = await this.messaging.createMessage(to, content);
    await this.messaging.sendMessage(message);
    await this.storage.saveMessage(message);
  }

  // File sharing functionality
  async shareFile(to: string, file: File): Promise<string> {
    const transferId = await this.fileTransfer.initiateTransfer(to, file);
    return transferId;
  }

  // Contact management
  async addContact(qrCode: string): Promise<Contact> {
    const contact = await this.contacts.parseQRCode(qrCode);
    await this.storage.saveContact(contact);
    return contact;
  }
}
```

### 3. **Data Access Layer** (`src/utils/storage/`)
```typescript
// Local storage management with encryption
export class SecureLocalStorage {
  private db: IDBDatabase;
  private encryptionKey: CryptoKey;

  async saveMessage(message: EncryptedMessage): Promise<void> {
    const transaction = this.db.transaction(['messages'], 'readwrite');
    const store = transaction.objectStore('messages');
    
    const encryptedData = await this.encryptForStorage(message);
    await store.add(encryptedData);
  }

  async getMessages(conversationId: string): Promise<Message[]> {
    const transaction = this.db.transaction(['messages'], 'readonly');
    const store = transaction.objectStore('messages');
    const index = store.index('conversationId');
    
    const encryptedMessages = await index.getAll(conversationId);
    return Promise.all(
      encryptedMessages.map(msg => this.decryptFromStorage(msg))
    );
  }

  private async encryptForStorage(data: any): Promise<EncryptedStorageItem> {
    const jsonData = JSON.stringify(data);
    const encoded = new TextEncoder().encode(jsonData);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      this.encryptionKey,
      encoded
    );
    
    return { iv, data: encrypted, timestamp: Date.now() };
  }
}
```

---

## 🔐 Security Implementation

### End-to-End Encryption
```typescript
export class E2EEncryption {
  private keyPair: CryptoKeyPair;
  private sharedKeys: Map<string, CryptoKey> = new Map();

  // Generate device identity
  async generateIdentity(): Promise<DeviceIdentity> {
    this.keyPair = await crypto.subtle.generateKey(
      {
        name: 'ECDH',
        namedCurve: 'P-256'
      },
      true,
      ['deriveKey']
    );

    const publicKeyBuffer = await crypto.subtle.exportKey(
      'raw',
      this.keyPair.publicKey
    );

    return {
      deviceId: await this.generateDeviceId(),
      publicKey: Array.from(new Uint8Array(publicKeyBuffer)),
      createdAt: Date.now()
    };
  }

  // Derive shared key for communication
  async deriveSharedKey(contactPublicKey: number[]): Promise<CryptoKey> {
    const publicKey = await crypto.subtle.importKey(
      'raw',
      new Uint8Array(contactPublicKey),
      { name: 'ECDH', namedCurve: 'P-256' },
      false,
      []
    );

    return crypto.subtle.deriveKey(
      { name: 'ECDH', public: publicKey },
      this.keyPair.privateKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  // Message encryption with forward secrecy
  async encryptMessage(
    contactId: string,
    message: string
  ): Promise<EncryptedMessage> {
    const sharedKey = this.sharedKeys.get(contactId);
    if (!sharedKey) throw new Error('No shared key for contact');

    // Generate new IV for each message
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const messageData = new TextEncoder().encode(message);

    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      sharedKey,
      messageData
    );

    return {
      id: crypto.randomUUID(),
      from: await this.getDeviceId(),
      to: contactId,
      iv: Array.from(iv),
      data: Array.from(new Uint8Array(encrypted)),
      timestamp: Date.now(),
      type: 'text'
    };
  }

  // File encryption with chunking
  async encryptFile(
    contactId: string,
    file: File
  ): Promise<EncryptedFilePackage> {
    const sharedKey = this.sharedKeys.get(contactId);
    if (!sharedKey) throw new Error('No shared key for contact');

    // Generate file-specific key
    const fileKey = await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt']
    );

    // Encrypt the file key with shared key
    const fileKeyBuffer = await crypto.subtle.exportKey('raw', fileKey);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encryptedFileKey = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      sharedKey,
      fileKeyBuffer
    );

    // Chunk and encrypt file
    const chunks = await this.chunkFile(file, 4 * 1024 * 1024); // 4MB chunks
    const encryptedChunks = await Promise.all(
      chunks.map((chunk, index) => this.encryptChunk(chunk, fileKey, index))
    );

    return {
      id: crypto.randomUUID(),
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      encryptedFileKey: Array.from(new Uint8Array(encryptedFileKey)),
      fileKeyIV: Array.from(iv),
      chunks: encryptedChunks,
      timestamp: Date.now()
    };
  }
}
```

### File Transfer Protocol
```typescript
export class ChunkedFileTransfer {
  private activeTransfers: Map<string, FileTransfer> = new Map();
  private chunkSize = 4 * 1024 * 1024; // 4MB chunks

  async initiateTransfer(
    contactId: string,
    encryptedFile: EncryptedFilePackage
  ): Promise<string> {
    const transferId = crypto.randomUUID();
    
    const transfer: FileTransfer = {
      id: transferId,
      contactId,
      file: encryptedFile,
      status: 'initiating',
      progress: 0,
      startedAt: Date.now(),
      chunksTransferred: 0,
      totalChunks: encryptedFile.chunks.length
    };

    this.activeTransfers.set(transferId, transfer);

    // Send transfer initiation
    await this.sendTransferRequest(contactId, {
      type: 'file-transfer-request',
      transferId,
      fileName: encryptedFile.fileName,
      fileSize: encryptedFile.fileSize,
      fileType: encryptedFile.fileType,
      chunkCount: encryptedFile.chunks.length
    });

    return transferId;
  }

  async handleTransferRequest(
    fromContactId: string,
    request: FileTransferRequest
  ): Promise<void> {
    // Auto-accept or show user prompt based on settings
    const accepted = await this.shouldAcceptTransfer(request);
    
    if (accepted) {
      await this.sendTransferResponse(fromContactId, {
        type: 'file-transfer-accept',
        transferId: request.transferId
      });
      
      await this.prepareReceive(request);
    } else {
      await this.sendTransferResponse(fromContactId, {
        type: 'file-transfer-reject',
        transferId: request.transferId,
        reason: 'User declined'
      });
    }
  }

  async transferChunk(
    transferId: string,
    chunkIndex: number
  ): Promise<void> {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) throw new Error('Transfer not found');

    const chunk = transfer.file.chunks[chunkIndex];
    
    await this.sendChunkData(transfer.contactId, {
      type: 'file-chunk',
      transferId,
      chunkIndex,
      data: chunk.data,
      iv: chunk.iv,
      checksum: chunk.checksum
    });

    // Update progress
    transfer.chunksTransferred++;
    transfer.progress = transfer.chunksTransferred / transfer.totalChunks;
    
    this.emit('transfer-progress', {
      transferId,
      progress: transfer.progress
    });

    if (transfer.chunksTransferred === transfer.totalChunks) {
      transfer.status = 'completed';
      this.emit('transfer-complete', { transferId });
    }
  }

  async receiveChunk(
    fromContactId: string,
    chunkData: FileChunkMessage
  ): Promise<void> {
    const transfer = this.activeTransfers.get(chunkData.transferId);
    if (!transfer) throw new Error('Transfer not found');

    // Verify checksum
    const calculatedChecksum = await this.calculateChecksum(chunkData.data);
    if (calculatedChecksum !== chunkData.checksum) {
      throw new Error('Chunk checksum mismatch');
    }

    // Store chunk
    transfer.receivedChunks[chunkData.chunkIndex] = {
      data: chunkData.data,
      iv: chunkData.iv,
      received: true
    };

    // Update progress
    const receivedCount = Object.keys(transfer.receivedChunks).length;
    transfer.progress = receivedCount / transfer.file.chunks.length;
    
    this.emit('transfer-progress', {
      transferId: chunkData.transferId,
      progress: transfer.progress
    });

    // Check if complete
    if (receivedCount === transfer.file.chunks.length) {
      await this.assembleFile(chunkData.transferId);
    }
  }

  private async assembleFile(transferId: string): Promise<void> {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) throw new Error('Transfer not found');

    // Decrypt and combine chunks
    const decryptedChunks = await Promise.all(
      transfer.file.chunks.map(async (_, index) => {
        const chunk = transfer.receivedChunks[index];
        return this.decryptChunk(chunk, transfer.fileKey);
      })
    );

    // Create blob from chunks
    const fileBlob = new Blob(decryptedChunks, {
      type: transfer.file.fileType
    });

    // Save to downloads or show save dialog
    await this.saveReceivedFile(fileBlob, transfer.file.fileName);
    
    transfer.status = 'completed';
    this.emit('transfer-complete', { transferId, file: fileBlob });
  }
}
```

---

## 🌐 P2P Connection Management

### WebRTC Connection Handler
```typescript
export class P2PConnectionManager {
  private connections: Map<string, RTCPeerConnection> = new Map();
  private dataChannels: Map<string, RTCDataChannel> = new Map();
  private messageQueues: Map<string, MessageQueue> = new Map();

  private readonly rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun.services.mozilla.com:3478' }
    ],
    iceCandidatePoolSize: 3,
    bundlePolicy: 'max-bundle',
    rtcpMuxPolicy: 'require'
  };

  async createConnection(
    contactId: string,
    isInitiator: boolean = false
  ): Promise<RTCPeerConnection> {
    const pc = new RTCPeerConnection(this.rtcConfig);
    
    // Setup event handlers
    this.setupConnectionHandlers(pc, contactId);
    
    if (isInitiator) {
      // Create data channel for messaging
      const messageChannel = pc.createDataChannel('messages', {
        ordered: true,
        maxRetransmits: 3
      });
      
      // Create data channel for file transfer
      const fileChannel = pc.createDataChannel('files', {
        ordered: false, // For better performance
        maxPacketLifeTime: 3000
      });
      
      this.setupDataChannelHandlers(messageChannel, contactId, 'messages');
      this.setupDataChannelHandlers(fileChannel, contactId, 'files');
    } else {
      // Wait for incoming data channels
      pc.ondatachannel = (event) => {
        const channel = event.channel;
        this.setupDataChannelHandlers(channel, contactId, channel.label);
      };
    }
    
    this.connections.set(contactId, pc);
    return pc;
  }

  private setupConnectionHandlers(
    pc: RTCPeerConnection,
    contactId: string
  ): void {
    pc.onconnectionstatechange = () => {
      console.log(`Connection to ${contactId}: ${pc.connectionState}`);
      
      switch (pc.connectionState) {
        case 'connected':
          this.emit('peer-connected', { contactId });
          break;
        case 'disconnected':
        case 'failed':
          this.handleConnectionFailure(contactId);
          break;
        case 'closed':
          this.cleanup(contactId);
          break;
      }
    };

    pc.onicecandidateerror = (event) => {
      console.error('ICE candidate error:', event);
      this.emit('ice-error', { contactId, error: event });
    };

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalingMessage(contactId, {
          type: 'ice-candidate',
          candidate: event.candidate
        });
      }
    };
  }

  private setupDataChannelHandlers(
    channel: RTCDataChannel,
    contactId: string,
    channelType: string
  ): void {
    channel.onopen = () => {
      console.log(`${channelType} channel opened for ${contactId}`);
      this.dataChannels.set(`${contactId}-${channelType}`, channel);
      
      // Process any queued messages
      const queue = this.messageQueues.get(contactId);
      if (queue) {
        queue.processQueue(channel);
      }
    };

    channel.onmessage = (event) => {
      this.handleChannelMessage(contactId, channelType, event.data);
    };

    channel.onerror = (error) => {
      console.error(`${channelType} channel error:`, error);
      this.emit('channel-error', { contactId, channelType, error });
    };

    channel.onclose = () => {
      console.log(`${channelType} channel closed for ${contactId}`);
      this.dataChannels.delete(`${contactId}-${channelType}`);
    };
  }

  async sendMessage(
    contactId: string,
    message: any,
    channelType: string = 'messages'
  ): Promise<void> {
    const channel = this.dataChannels.get(`${contactId}-${channelType}`);
    
    if (channel && channel.readyState === 'open') {
      channel.send(JSON.stringify(message));
    } else {
      // Queue message for later delivery
      const queue = this.messageQueues.get(contactId) || new MessageQueue();
      queue.add(message, channelType);
      this.messageQueues.set(contactId, queue);
    }
  }

  private async handleConnectionFailure(contactId: string): Promise<void> {
    console.log(`Attempting to reconnect to ${contactId}`);
    
    // Implement exponential backoff for reconnection
    const maxRetries = 5;
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        await this.sleep(Math.pow(2, retryCount) * 1000); // Exponential backoff
        await this.reconnect(contactId);
        break;
      } catch (error) {
        retryCount++;
        console.error(`Reconnection attempt ${retryCount} failed:`, error);
      }
    }
    
    if (retryCount === maxRetries) {
      this.emit('connection-failed', { contactId });
    }
  }

  private async reconnect(contactId: string): Promise<void> {
    // Clean up old connection
    this.cleanup(contactId);
    
    // Create new connection
    await this.createConnection(contactId, true);
    
    // Re-establish communication
    await this.initiateHandshake(contactId);
  }
}
```

---

## 📱 User Interface Components

### Chat Interface
```typescript
// Main chat component
export const ChatInterface: React.FC<{ contactId: string }> = ({ 
  contactId 
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  const { sendMessage, sendFile } = useAirChat();

  // Auto-scroll to bottom on new messages
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (inputText.trim()) {
      await sendMessage(contactId, inputText);
      setInputText('');
    }
  };

  const handleFileDrop = async (files: FileList) => {
    setIsDragging(false);
    
    for (const file of Array.from(files)) {
      await sendFile(contactId, file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  return (
    <div 
      className={`chat-interface ${isDragging ? 'dragging' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={(e) => {
        e.preventDefault();
        handleFileDrop(e.dataTransfer.files);
      }}
    >
      <div className="messages-container">
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isOwn={message.from === 'self'}
          />
        ))}
        <div ref={chatEndRef} />
      </div>

      {isDragging && (
        <div className="drop-overlay">
          <div className="drop-indicator">
            📁 Drop files to share
          </div>
        </div>
      )}

      <div className="input-container">
        <button
          className="attach-button"
          onClick={() => document.getElementById('file-input')?.click()}
        >
          📎
        </button>
        
        <input
          type="text"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          placeholder="Type a message..."
          className="message-input"
        />
        
        <button
          onClick={handleSendMessage}
          disabled={!inputText.trim()}
          className="send-button"
        >
          🚀
        </button>
        
        <input
          id="file-input"
          type="file"
          multiple
          hidden
          onChange={(e) => e.target.files && handleFileDrop(e.target.files)}
        />
      </div>
    </div>
  );
};

// Message bubble component
export const MessageBubble: React.FC<{
  message: Message;
  isOwn: boolean;
}> = ({ message, isOwn }) => {
  return (
    <div className={`message-bubble ${isOwn ? 'own' : 'other'}`}>
      {message.type === 'text' && (
        <div className="message-text">{message.content}</div>
      )}
      
      {message.type === 'file' && (
        <FileAttachment file={message.file} />
      )}
      
      <div className="message-metadata">
        <span className="timestamp">
          {new Date(message.timestamp).toLocaleTimeString()}
        </span>
        {isOwn && (
          <span className={`delivery-status ${message.status}`}>
            {message.status === 'sent' && '✓'}
            {message.status === 'delivered' && '✓✓'}
            {message.status === 'read' && '✓✓'}
          </span>
        )}
      </div>
    </div>
  );
};
```

### File Transfer UI
```typescript
export const FileTransferPanel: React.FC = () => {
  const { activeTransfers } = useFileTransfer();

  return (
    <div className="file-transfer-panel">
      <h3>File Transfers</h3>
      
      {activeTransfers.map((transfer) => (
        <TransferItem key={transfer.id} transfer={transfer} />
      ))}
      
      {activeTransfers.length === 0 && (
        <div className="no-transfers">
          No active transfers
        </div>
      )}
    </div>
  );
};

export const TransferItem: React.FC<{
  transfer: FileTransfer;
}> = ({ transfer }) => {
  const getStatusIcon = () => {
    switch (transfer.status) {
      case 'initiating': return '🔄';
      case 'transferring': return '📤';
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'cancelled': return '🚫';
      default: return '📄';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatSpeed = (bytesPerSecond: number) => {
    return `${formatFileSize(bytesPerSecond)}/s`;
  };

  const estimatedTimeRemaining = () => {
    if (transfer.status !== 'transferring' || transfer.speed === 0) {
      return '';
    }
    
    const remainingBytes = transfer.totalSize * (1 - transfer.progress);
    const seconds = remainingBytes / transfer.speed;
    
    if (seconds < 60) {
      return `${Math.ceil(seconds)}s remaining`;
    } else if (seconds < 3600) {
      return `${Math.ceil(seconds / 60)}m remaining`;
    } else {
      return `${Math.ceil(seconds / 3600)}h remaining`;
    }
  };

  return (
    <div className="transfer-item">
      <div className="transfer-header">
        <span className="status-icon">{getStatusIcon()}</span>
        <span className="filename">{transfer.fileName}</span>
        <span className="file-size">{formatFileSize(transfer.totalSize)}</span>
      </div>
      
      {transfer.status === 'transferring' && (
        <>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${transfer.progress * 100}%` }}
            />
          </div>
          
          <div className="transfer-stats">
            <span className="speed">{formatSpeed(transfer.speed)}</span>
            <span className="eta">{estimatedTimeRemaining()}</span>
            <span className="progress-text">
              {Math.round(transfer.progress * 100)}%
            </span>
          </div>
        </>
      )}
      
      {transfer.status === 'completed' && (
        <div className="transfer-actions">
          <button onClick={() => transfer.openFile()}>
            Open File
          </button>
          <button onClick={() => transfer.showInFolder()}>
            Show in Folder
          </button>
        </div>
      )}
    </div>
  );
};
```

---

## 🔧 Build Configuration

### Vite Configuration
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        maximumFileSizeToCacheInBytes: 5000000 // 5MB
      },
      manifest: {
        name: 'AirChat - Secure P2P Messaging',
        short_name: 'AirChat',
        description: 'End-to-end encrypted messaging and file sharing',
        theme_color: '#2196f3',
        background_color: '#ffffff',
        display: 'standalone',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'icon-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icon-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      }
    })
  ],
  
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  },
  
  build: {
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          crypto: ['crypto-js'],
          ui: ['@emotion/react', '@emotion/styled']
        }
      }
    }
  },
  
  server: {
    port: 3000,
    host: true // Allow external connections for testing
  },
  
  preview: {
    port: 3000,
    host: true
  }
});
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable", "WebWorker"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/core/*": ["src/core/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": ["src", "vite.config.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

---

## 🧪 Testing Implementation

### Test Structure
```typescript
// Integration test for message flow
describe('AirChat Message Flow', () => {
  let app1: AirChatApp;
  let app2: AirChatApp;
  let contact1: Contact;
  let contact2: Contact;

  beforeEach(async () => {
    // Setup two app instances
    app1 = new AirChatApp({
      signalingServer: 'ws://localhost:8787',
      storage: { prefix: 'test1_' }
    });
    
    app2 = new AirChatApp({
      signalingServer: 'ws://localhost:8787',
      storage: { prefix: 'test2_' }
    });

    // Generate identities
    const identity1 = await app1.generateIdentity();
    const identity2 = await app2.generateIdentity();

    // Exchange contacts
    contact1 = await app2.addContact(identity1.qrCode);
    contact2 = await app1.addContact(identity2.qrCode);
  });

  test('should send and receive text message', async () => {
    const message = 'Hello from app1!';
    
    // Send message from app1 to app2
    await app1.sendMessage(contact2.id, message);
    
    // Wait for message to be received
    const receivedMessage = await waitForMessage(app2, contact1.id);
    
    expect(receivedMessage.content).toBe(message);
    expect(receivedMessage.from).toBe(contact1.id);
  });

  test('should transfer file successfully', async () => {
    const testFile = new File(['test content'], 'test.txt', {
      type: 'text/plain'
    });
    
    // Start file transfer
    const transferId = await app1.shareFile(contact2.id, testFile);
    
    // Wait for transfer completion
    const receivedFile = await waitForFileTransfer(app2, transferId);
    
    expect(receivedFile.name).toBe('test.txt');
    expect(receivedFile.size).toBe(testFile.size);
    
    // Verify file content
    const content = await receivedFile.text();
    expect(content).toBe('test content');
  });

  test('should handle connection failure gracefully', async () => {
    // Send message while connected
    await app1.sendMessage(contact2.id, 'Message 1');
    
    // Simulate connection failure
    await app1.disconnect(contact2.id);
    
    // Send message while disconnected (should queue)
    await app1.sendMessage(contact2.id, 'Message 2');
    
    // Reconnect
    await app1.reconnect(contact2.id);
    
    // Both messages should be received
    const messages = await waitForMessages(app2, contact1.id, 2);
    expect(messages).toHaveLength(2);
    expect(messages[0].content).toBe('Message 1');
    expect(messages[1].content).toBe('Message 2');
  });
});

// Performance test for file transfer
describe('File Transfer Performance', () => {
  test('should transfer large file efficiently', async () => {
    const largeFile = generateTestFile(50 * 1024 * 1024); // 50MB
    
    const startTime = Date.now();
    const transferId = await app1.shareFile(contact2.id, largeFile);
    
    const receivedFile = await waitForFileTransfer(app2, transferId);
    const endTime = Date.now();
    
    const transferTime = (endTime - startTime) / 1000; // seconds
    const transferSpeed = largeFile.size / transferTime; // bytes/second
    
    // Should transfer at least 1MB/s
    expect(transferSpeed).toBeGreaterThan(1024 * 1024);
    
    // File should be intact
    expect(receivedFile.size).toBe(largeFile.size);
  });
});
```

---

## 📋 Implementation Checklist

### Phase 1: Core Functionality (Week 1-2)
- [ ] **Project Setup**
  - [ ] Create `packages/airchat-client` package
  - [ ] Setup Vite + React + TypeScript
  - [ ] Configure ESLint and Prettier
  - [ ] Setup testing framework (Vitest + Testing Library)

- [ ] **Core Messaging**
  - [ ] Extend existing P2P messaging
  - [ ] Implement E2E encryption for messages
  - [ ] Add message persistence (IndexedDB)
  - [ ] Create chat interface components

- [ ] **File Transfer**
  - [ ] Implement chunked file transfer
  - [ ] Add file encryption/decryption
  - [ ] Create file transfer UI components
  - [ ] Add drag & drop support

- [ ] **Contact Management**
  - [ ] QR code generation and scanning
  - [ ] Contact storage and management
  - [ ] Device identity generation

### Phase 2: Advanced Features (Week 3-4)
- [ ] **Group Messaging**
  - [ ] Multi-peer connection management
  - [ ] Group encryption key management
  - [ ] Group chat UI

- [ ] **Device Synchronization**
  - [ ] Cross-device pairing mechanism
  - [ ] Message sync across devices
  - [ ] Conflict resolution

- [ ] **File Management**
  - [ ] File organization and search
  - [ ] Transfer history
  - [ ] Storage management

- [ ] **Performance & Polish**
  - [ ] Offline support (Service Worker)
  - [ ] Push notifications
  - [ ] Performance optimization
  - [ ] Mobile responsive design

### Phase 3: Production Ready (Week 5-6)
- [ ] **Security Audit**
  - [ ] Penetration testing
  - [ ] Code review for security vulnerabilities
  - [ ] Crypto implementation audit

- [ ] **Deployment**
  - [ ] CI/CD pipeline setup
  - [ ] Production build optimization
  - [ ] Monitoring and analytics
  - [ ] Error reporting

- [ ] **Documentation**
  - [ ] User guide
  - [ ] API documentation
  - [ ] Security whitepaper
  - [ ] Privacy policy

**This technical specification provides the complete roadmap for implementing AirChat. Each component is designed to work together seamlessly while maintaining security, performance, and user experience standards.** 