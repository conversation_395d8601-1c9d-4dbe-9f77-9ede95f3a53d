# 📁 AirChat Project Structure & Setup Guide

## Complete Project Architecture

This document defines the complete file structure, dependencies, and setup process for implementing AirChat.

---

## 🏗️ Project Structure

```
workspace/
├── packages/
│   ├── airchat-client/                    # Main AirChat application
│   │   ├── public/
│   │   │   ├── index.html                 # Entry point
│   │   │   ├── manifest.json              # PWA manifest
│   │   │   ├── sw.js                      # Service worker
│   │   │   ├── icons/                     # App icons (192x192, 512x512)
│   │   │   │   ├── icon-192.png
│   │   │   │   ├── icon-512.png
│   │   │   │   └── favicon.ico
│   │   │   └── robots.txt
│   │   ├── src/
│   │   │   ├── App.tsx                    # Root React component
│   │   │   ├── main.tsx                   # Application entry
│   │   │   ├── vite-env.d.ts              # Vite type definitions
│   │   │   ├── core/                      # Business logic layer
│   │   │   │   ├── AirChatApp.ts          # Main application controller
│   │   │   │   ├── messaging/
│   │   │   │   │   ├── SecureMessaging.ts # Message encryption/handling
│   │   │   │   │   ├── MessageQueue.ts    # Offline message queue
│   │   │   │   │   ├── GroupManager.ts    # Group chat management
│   │   │   │   │   └── MessageTypes.ts    # Message type definitions
│   │   │   │   ├── files/
│   │   │   │   │   ├── FileTransfer.ts    # P2P file sharing
│   │   │   │   │   ├── ChunkedTransfer.ts # Large file handling
│   │   │   │   │   ├── FileEncryption.ts  # File encryption
│   │   │   │   │   └── FilePreview.ts     # File preview generation
│   │   │   │   ├── contacts/
│   │   │   │   │   ├── ContactManager.ts  # Contact CRUD operations
│   │   │   │   │   ├── QRCodeManager.ts   # QR code generation/scanning
│   │   │   │   │   ├── PresenceManager.ts # Online/offline status
│   │   │   │   │   └── DeviceIdentity.ts  # Device identity management
│   │   │   │   ├── sync/
│   │   │   │   │   ├── DeviceSync.ts      # Cross-device synchronization
│   │   │   │   │   ├── ConflictResolver.ts # Sync conflict resolution
│   │   │   │   │   └── BackupManager.ts   # Data backup/restore
│   │   │   │   └── p2p/
│   │   │   │       ├── P2PConnectionManager.ts # WebRTC connections
│   │   │   │       ├── SignalingClient.ts      # Signaling server comm
│   │   │   │       ├── NetworkManager.ts       # Network status/quality
│   │   │   │       └── ReconnectionManager.ts  # Connection recovery
│   │   │   ├── components/                # React UI components
│   │   │   │   ├── layout/
│   │   │   │   │   ├── AppShell.tsx       # Main app layout
│   │   │   │   │   ├── Sidebar.tsx        # Navigation sidebar
│   │   │   │   │   ├── StatusBar.tsx      # Connection/transfer status
│   │   │   │   │   └── MobileLayout.tsx   # Mobile-optimized layout
│   │   │   │   ├── chat/
│   │   │   │   │   ├── ChatInterface.tsx  # Main chat component
│   │   │   │   │   ├── MessageBubble.tsx  # Individual message display
│   │   │   │   │   ├── MessageInput.tsx   # Message composition
│   │   │   │   │   ├── FileAttachment.tsx # File display in chat
│   │   │   │   │   ├── TypingIndicator.tsx # Typing status
│   │   │   │   │   ├── MessageSearch.tsx  # Search within conversation
│   │   │   │   │   └── GroupChatPanel.tsx # Group management UI
│   │   │   │   ├── contacts/
│   │   │   │   │   ├── ContactList.tsx    # Contact list display
│   │   │   │   │   ├── ContactCard.tsx    # Individual contact card
│   │   │   │   │   ├── AddContact.tsx     # Add contact flow
│   │   │   │   │   ├── QRScanner.tsx      # QR code scanner
│   │   │   │   │   ├── QRDisplay.tsx      # QR code display
│   │   │   │   │   └── ContactProfile.tsx # Contact details/settings
│   │   │   │   ├── files/
│   │   │   │   │   ├── FileManager.tsx    # File organization
│   │   │   │   │   ├── FileGrid.tsx       # File grid display
│   │   │   │   │   ├── FilePreview.tsx    # File preview modal
│   │   │   │   │   ├── TransferProgress.tsx # Transfer progress UI
│   │   │   │   │   ├── DropZone.tsx       # Drag & drop area
│   │   │   │   │   └── FileSearch.tsx     # File search component
│   │   │   │   ├── calls/
│   │   │   │   │   ├── VoiceCall.tsx      # Voice call interface
│   │   │   │   │   ├── VideoCall.tsx      # Video call interface
│   │   │   │   │   ├── CallControls.tsx   # Call control buttons
│   │   │   │   │   └── IncomingCall.tsx   # Incoming call notification
│   │   │   │   ├── settings/
│   │   │   │   │   ├── SettingsPanel.tsx  # Main settings
│   │   │   │   │   ├── PrivacySettings.tsx # Privacy controls
│   │   │   │   │   ├── AppearanceSettings.tsx # Theme/UI settings
│   │   │   │   │   ├── DeviceSettings.tsx # Device management
│   │   │   │   │   └── SecuritySettings.tsx # Security configuration
│   │   │   │   └── common/
│   │   │   │       ├── Modal.tsx          # Reusable modal
│   │   │   │       ├── Button.tsx         # Styled button
│   │   │   │       ├── Input.tsx          # Form input
│   │   │   │       ├── LoadingSpinner.tsx # Loading indicator
│   │   │   │       ├── Toast.tsx          # Notification toast
│   │   │   │       └── ProgressBar.tsx    # Progress indicator
│   │   │   ├── hooks/                     # React hooks
│   │   │   │   ├── useAirChat.ts          # Main app state hook
│   │   │   │   ├── useP2PConnection.ts    # P2P connection state
│   │   │   │   ├── useFileTransfer.ts     # File transfer state
│   │   │   │   ├── useDeviceSync.ts       # Device sync state
│   │   │   │   ├── useContacts.ts         # Contact management
│   │   │   │   ├── useMessages.ts         # Message state
│   │   │   │   ├── useSettings.ts         # App settings
│   │   │   │   └── useNotifications.ts    # Push notifications
│   │   │   ├── utils/                     # Utility functions
│   │   │   │   ├── crypto/
│   │   │   │   │   ├── encryption.ts      # Crypto operations
│   │   │   │   │   ├── keyDerivation.ts   # Key generation
│   │   │   │   │   ├── fingerprint.ts     # Key fingerprints
│   │   │   │   │   └── secureRandom.ts    # Secure randomness
│   │   │   │   ├── storage/
│   │   │   │   │   ├── indexedDB.ts       # IndexedDB wrapper
│   │   │   │   │   ├── encryption.ts      # Storage encryption
│   │   │   │   │   ├── migration.ts       # Data migration
│   │   │   │   │   └── backup.ts          # Backup/restore
│   │   │   │   ├── files/
│   │   │   │   │   ├── fileUtils.ts       # File manipulation
│   │   │   │   │   ├── mimeTypes.ts       # MIME type detection
│   │   │   │   │   ├── thumbnails.ts      # Thumbnail generation
│   │   │   │   │   └── compression.ts     # File compression
│   │   │   │   ├── ui/
│   │   │   │   │   ├── themes.ts          # Theme definitions
│   │   │   │   │   ├── responsive.ts      # Responsive utilities
│   │   │   │   │   ├── animations.ts      # Animation helpers
│   │   │   │   │   └── accessibility.ts   # A11y utilities
│   │   │   │   ├── network/
│   │   │   │   │   ├── connectivity.ts    # Network status
│   │   │   │   │   ├── bandwidth.ts       # Bandwidth estimation
│   │   │   │   │   ├── retry.ts           # Retry logic
│   │   │   │   │   └── timeout.ts         # Timeout handling
│   │   │   │   └── validation/
│   │   │   │       ├── messageValidation.ts # Message validation
│   │   │   │       ├── fileValidation.ts   # File validation
│   │   │   │       ├── contactValidation.ts # Contact validation
│   │   │   │       └── qrValidation.ts     # QR code validation
│   │   │   ├── types/                     # TypeScript type definitions
│   │   │   │   ├── index.ts               # Main exports
│   │   │   │   ├── messages.ts            # Message types
│   │   │   │   ├── files.ts               # File types
│   │   │   │   ├── contacts.ts            # Contact types
│   │   │   │   ├── p2p.ts                 # P2P connection types
│   │   │   │   ├── crypto.ts              # Cryptography types
│   │   │   │   └── ui.ts                  # UI component types
│   │   │   ├── styles/                    # CSS and styling
│   │   │   │   ├── globals.css            # Global styles
│   │   │   │   ├── variables.css          # CSS variables
│   │   │   │   ├── themes/
│   │   │   │   │   ├── light.css          # Light theme
│   │   │   │   │   ├── dark.css           # Dark theme
│   │   │   │   │   └── high-contrast.css  # Accessibility theme
│   │   │   │   ├── components/
│   │   │   │   │   ├── chat.css           # Chat interface styles
│   │   │   │   │   ├── contacts.css       # Contact list styles
│   │   │   │   │   ├── files.css          # File manager styles
│   │   │   │   │   └── settings.css       # Settings panel styles
│   │   │   │   └── mobile.css             # Mobile-specific styles
│   │   │   └── workers/                   # Web Workers
│   │   │       ├── crypto.worker.ts       # Crypto operations worker
│   │   │       ├── file.worker.ts         # File processing worker
│   │   │       └── sync.worker.ts         # Background sync worker
│   │   ├── tests/                         # Test files
│   │   │   ├── setup.ts                   # Test setup
│   │   │   ├── __mocks__/                 # Mock implementations
│   │   │   ├── unit/                      # Unit tests
│   │   │   │   ├── core/                  # Core logic tests
│   │   │   │   ├── components/            # Component tests
│   │   │   │   ├── hooks/                 # Hook tests
│   │   │   │   └── utils/                 # Utility tests
│   │   │   ├── integration/               # Integration tests
│   │   │   │   ├── messaging.test.ts      # Message flow tests
│   │   │   │   ├── fileTransfer.test.ts   # File transfer tests
│   │   │   │   └── deviceSync.test.ts     # Device sync tests
│   │   │   └── e2e/                       # End-to-end tests
│   │   │       ├── chat.spec.ts           # Chat workflow tests
│   │   │       ├── contacts.spec.ts       # Contact management tests
│   │   │       └── files.spec.ts          # File sharing tests
│   │   ├── package.json                   # Package configuration
│   │   ├── tsconfig.json                  # TypeScript config
│   │   ├── vite.config.ts                 # Vite configuration
│   │   ├── vitest.config.ts               # Vitest test config
│   │   ├── playwright.config.ts           # Playwright E2E config
│   │   ├── .eslintrc.js                   # ESLint configuration
│   │   ├── .prettierrc                    # Prettier configuration
│   │   └── README.md                      # Package documentation
│   │
│   ├── airchat-signaling/                 # Enhanced signaling server
│   │   ├── src/
│   │   │   ├── index.ts                   # Worker entry point
│   │   │   ├── AirChatSignaling.ts        # Extended signaling logic
│   │   │   ├── DeviceRegistry.ts          # Device management
│   │   │   ├── GroupCoordinator.ts        # Group management
│   │   │   ├── PresenceTracker.ts         # Presence tracking
│   │   │   └── RateLimiter.ts             # Rate limiting
│   │   ├── test/
│   │   │   ├── signaling.test.ts          # Signaling tests
│   │   │   └── performance.test.ts        # Performance tests
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   ├── vitest.config.mts
│   │   ├── wrangler.jsonc                 # Cloudflare config
│   │   └── worker-configuration.d.ts
│   │
│   └── shared/                            # Shared utilities
│       ├── src/
│       │   ├── types/                     # Shared types
│       │   │   ├── signaling.ts           # Signaling protocol types
│       │   │   ├── messages.ts            # Message format types
│       │   │   └── errors.ts              # Error types
│       │   ├── utils/
│       │   │   ├── validation.ts          # Shared validation
│       │   │   ├── constants.ts           # App constants
│       │   │   └── helpers.ts             # Shared helpers
│       │   └── protocols/
│       │       ├── messaging.ts           # Message protocol
│       │       ├── fileTransfer.ts        # File transfer protocol
│       │       └── deviceSync.ts          # Device sync protocol
│       ├── package.json
│       ├── tsconfig.json
│       └── README.md
│
├── .cursor/                               # AI development context
│   ├── docs/                             # Comprehensive documentation
│   │   ├── 01-project-overview.md
│   │   ├── 02-development-workflow.md
│   │   ├── 03-architecture-patterns.md
│   │   ├── 04-cost-optimization-strategy.md
│   │   ├── 05-p2p-foundation-guide.md
│   │   ├── 06-p2p-applications-brainstorm.md
│   │   ├── 07-airchat-comprehensive-plan.md
│   │   ├── 08-airchat-technical-specification.md
│   │   ├── 09-airchat-user-stories.md
│   │   └── 10-airchat-project-structure.md
│   ├── context/
│   │   ├── .state.json                   # Development state
│   │   └── airchat-implementation-progress.md
│   └── rules/
│       ├── typescript-conventions.md
│       ├── development-standards.md
│       └── airchat-specific-rules.md
│
├── package.json                          # Root workspace config
├── pnpm-workspace.yaml                   # PNPM workspace config
├── pnpm-lock.yaml                        # Lock file
├── .gitignore                            # Git ignore rules
├── .eslintrc.js                          # Root ESLint config
├── .prettierrc                           # Root Prettier config
├── tsconfig.json                         # Root TypeScript config
└── README.md                             # Project documentation
```

---

## 📦 Package Dependencies

### AirChat Client (`packages/airchat-client/package.json`)

```json
{
  "name": "@airchat/client",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite --port 3000",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "playwright test",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write src/**/*.{ts,tsx,css,md}",
    "analyze": "npx vite-bundle-analyzer"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "zustand": "^4.4.1",
    "dexie": "^3.2.4",
    "qrcode": "^1.5.3",
    "qr-scanner": "^1.4.2",
    "file-saver": "^2.0.5",
    "fuse.js": "^6.6.2",
    "date-fns": "^2.30.0",
    "react-hotkeys-hook": "^4.4.1",
    "react-intersection-observer": "^9.5.2",
    "react-virtualized-auto-sizer": "^1.0.20",
    "react-window": "^1.8.8",
    "workbox-window": "^7.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@types/qrcode": "^1.5.1",
    "@types/file-saver": "^2.0.5",
    "@types/react-window": "^1.8.5",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "@vitest/ui": "^0.34.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/user-event": "^14.4.3",
    "@playwright/test": "^1.37.0",
    "vite": "^4.4.5",
    "vite-plugin-pwa": "^0.16.4",
    "vitest": "^0.34.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react": "^7.33.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "prettier": "^3.0.0",
    "typescript": "^5.0.2",
    "jsdom": "^22.1.0"
  }
}
```

### Shared Package (`packages/shared/package.json`)

```json
{
  "name": "@airchat/shared",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "main": "./dist/index.js",
  "types": "./dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "test": "vitest",
    "lint": "eslint src --ext ts --report-unused-disable-directives --max-warnings 0",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "zod": "^3.22.2"
  },
  "devDependencies": {
    "@types/node": "^20.5.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.45.0",
    "typescript": "^5.0.2",
    "vitest": "^0.34.0"
  }
}
```

### Enhanced Signaling (`packages/airchat-signaling/package.json`)

```json
{
  "name": "@airchat/signaling",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "wrangler dev",
    "deploy": "wrangler deploy",
    "build": "tsc",
    "test": "vitest",
    "type-check": "tsc --noEmit",
    "lint": "eslint src --ext ts --report-unused-disable-directives --max-warnings 0"
  },
  "dependencies": {
    "@airchat/shared": "workspace:*"
  },
  "devDependencies": {
    "@cloudflare/workers-types": "^4.20230821.0",
    "@types/node": "^20.5.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.45.0",
    "typescript": "^5.0.2",
    "vitest": "^0.34.0",
    "wrangler": "^3.6.0"
  }
}
```

### Root Workspace (`package.json`)

```json
{
  "name": "airchat-workspace",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "pnpm -r --parallel dev",
    "build": "pnpm -r build",
    "test": "pnpm -r test",
    "test:e2e": "pnpm -C packages/airchat-client test:e2e",
    "lint": "pnpm -r lint",
    "lint:fix": "pnpm -r lint:fix",
    "type-check": "pnpm -r type-check",
    "format": "prettier --write **/*.{ts,tsx,js,jsx,md,json}",
    "clean": "pnpm -r exec rm -rf dist node_modules",
    "deploy:signaling": "pnpm -C packages/airchat-signaling deploy",
    "deploy:client": "pnpm -C packages/airchat-client build && echo 'Deploy client to your hosting provider'",
    "prepare": "husky install"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "eslint": "^8.45.0",
    "husky": "^8.0.3",
    "lint-staged": "^13.2.3",
    "prettier": "^3.0.0",
    "typescript": "^5.0.2"
  },
  "lint-staged": {
    "*.{ts,tsx,js,jsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{md,json}": [
      "prettier --write"
    ]
  },
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=8.0.0"
  }
}
```

---

## ⚙️ Configuration Files

### TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable", "WebWorker"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": false,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/core/*": ["src/core/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"],
      "@/styles/*": ["src/styles/*"],
      "@airchat/shared": ["../shared/src/index.ts"]
    }
  },
  "include": [
    "src",
    "tests",
    "vite.config.ts",
    "vitest.config.ts",
    "playwright.config.ts"
  ],
  "exclude": ["node_modules", "dist"]
}
```

### Vite Configuration (`packages/airchat-client/vite.config.ts`)

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        maximumFileSizeToCacheInBytes: 5000000,
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          }
        ]
      },
      manifest: {
        name: 'AirChat - Secure P2P Messaging',
        short_name: 'AirChat',
        description: 'End-to-end encrypted messaging and file sharing',
        theme_color: '#2196f3',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'icons/icon-192.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any maskable'
          },
          {
            src: 'icons/icon-512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
        categories: ['communication', 'productivity', 'utilities'],
        shortcuts: [
          {
            name: 'New Chat',
            short_name: 'New Chat',
            description: 'Start a new conversation',
            url: '/?action=new-chat',
            icons: [{ src: 'icons/icon-192.png', sizes: '192x192' }]
          }
        ]
      }
    })
  ],
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/core': path.resolve(__dirname, './src/core'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/styles': path.resolve(__dirname, './src/styles')
    }
  },
  
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    '__DEV__': process.env.NODE_ENV === 'development'
  },
  
  build: {
    target: 'es2020',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          crypto: ['qrcode', 'qr-scanner'],
          storage: ['dexie'],
          ui: ['zustand', 'react-window']
        }
      }
    }
  },
  
  server: {
    port: 3000,
    host: '0.0.0.0',
    hmr: {
      overlay: true
    }
  },
  
  preview: {
    port: 3000,
    host: '0.0.0.0'
  },
  
  optimizeDeps: {
    include: ['react', 'react-dom', 'dexie', 'qrcode', 'qr-scanner']
  }
});
```

### ESLint Configuration (`.eslintrc.js`)

```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2020: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react/jsx-runtime'
  ],
  ignorePatterns: ['dist', '.eslintrc.js'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  plugins: ['react-refresh', '@typescript-eslint'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true }
    ],
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'react/prop-types': 'off',
    'react/display-name': 'off',
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'prefer-const': 'error',
    'no-var': 'error'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};
```

### Prettier Configuration (`.prettierrc`)

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "quoteProps": "as-needed"
}
```

---

## 🚀 Setup Instructions

### Prerequisites

```bash
# Required software
node --version    # v18.0.0 or higher
pnpm --version    # v8.0.0 or higher
git --version     # Latest stable

# Install pnpm if not installed
npm install -g pnpm@latest
```

### Project Initialization

```bash
# 1. Navigate to workspace root
cd workspace

# 2. Create AirChat client package
mkdir -p packages/airchat-client
cd packages/airchat-client

# 3. Initialize package
pnpm init

# 4. Install dependencies
pnpm add react react-dom zustand dexie qrcode qr-scanner file-saver \
  fuse.js date-fns react-hotkeys-hook react-intersection-observer \
  react-virtualized-auto-sizer react-window workbox-window

# 5. Install dev dependencies
pnpm add -D @types/react @types/react-dom @types/qrcode @types/file-saver \
  @types/react-window @typescript-eslint/eslint-plugin \
  @typescript-eslint/parser @vitejs/plugin-react @vitest/ui \
  @testing-library/react @testing-library/jest-dom \
  @testing-library/user-event @playwright/test vite vite-plugin-pwa \
  vitest eslint eslint-plugin-react eslint-plugin-react-hooks \
  eslint-plugin-react-refresh prettier typescript jsdom

# 6. Create enhanced signaling package
cd ../
mkdir -p airchat-signaling
cd airchat-signaling

# 7. Initialize signaling package
pnpm init
pnpm add @airchat/shared@workspace:*
pnpm add -D @cloudflare/workers-types @types/node @typescript-eslint/eslint-plugin \
  @typescript-eslint/parser eslint typescript vitest wrangler

# 8. Create shared package
cd ../
mkdir -p shared
cd shared

# 9. Initialize shared package
pnpm init
pnpm add zod
pnpm add -D @types/node @typescript-eslint/eslint-plugin \
  @typescript-eslint/parser eslint typescript vitest

# 10. Return to workspace root and install all dependencies
cd ../../
pnpm install
```

### Directory Structure Creation

```bash
# Create complete directory structure
mkdir -p packages/airchat-client/{public/icons,src/{core/{messaging,files,contacts,sync,p2p},components/{layout,chat,contacts,files,calls,settings,common},hooks,utils/{crypto,storage,files,ui,network,validation},types,styles/{themes,components},workers},tests/{__mocks__,unit/{core,components,hooks,utils},integration,e2e}}

mkdir -p packages/airchat-signaling/{src,test}

mkdir -p packages/shared/src/{types,utils,protocols}

# Create initial files
touch packages/airchat-client/public/{index.html,manifest.json,sw.js,robots.txt}
touch packages/airchat-client/public/icons/{icon-192.png,icon-512.png,favicon.ico}

# Create configuration files
touch packages/airchat-client/{package.json,tsconfig.json,vite.config.ts,vitest.config.ts,playwright.config.ts,.eslintrc.js,.prettierrc,README.md}

touch packages/airchat-signaling/{package.json,tsconfig.json,vitest.config.mts,wrangler.jsonc,worker-configuration.d.ts}

touch packages/shared/{package.json,tsconfig.json,README.md}
```

### Git Setup

```bash
# Initialize git repository (if not already done)
git init

# Create .gitignore
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/

# Cloudflare Workers
.wrangler/

# Testing
/test-results/
/playwright-report/
/playwright/.cache/

# PWA
sw.js.map
workbox-*.js
EOF
```

### Development Environment Setup

```bash
# Install Git hooks for code quality
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
npx husky add .husky/commit-msg 'npx --no -- commitlint --edit ${1}'

# Create VS Code workspace settings
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true
  }
}
EOF

# Create VS Code extensions recommendations
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-playwright.playwright",
    "ms-vscode.vscode-typescript-next"
  ]
}
EOF
```

### Verification

```bash
# Verify setup
pnpm type-check    # Should pass with no errors
pnpm lint          # Should pass with no errors
pnpm test          # Should run tests (may be empty initially)
pnpm build         # Should build all packages

# Start development
pnpm dev           # Starts all development servers
```

---

## 📋 Implementation Checklist

### Phase 1: Project Setup (Week 1)
- [ ] **Environment Setup**
  - [ ] Create project structure
  - [ ] Install all dependencies
  - [ ] Configure TypeScript, ESLint, Prettier
  - [ ] Setup testing framework (Vitest + Playwright)
  - [ ] Configure Git hooks and VS Code settings

- [ ] **Core Foundation**
  - [ ] Extend existing P2P messaging from base project
  - [ ] Create type definitions for all major interfaces
  - [ ] Setup IndexedDB for local storage
  - [ ] Implement basic React app shell
  - [ ] Create routing and navigation structure

### Phase 2: Core Features (Week 2-3)
- [ ] **Messaging System**
  - [ ] Enhanced secure messaging with E2E encryption
  - [ ] Message persistence and history
  - [ ] Real-time message synchronization
  - [ ] Delivery and read receipts
  - [ ] Message search functionality

- [ ] **File Transfer**
  - [ ] Chunked file transfer implementation
  - [ ] File encryption and decryption
  - [ ] Progress tracking and resumable transfers
  - [ ] File preview and management
  - [ ] Drag & drop interface

### Phase 3: Advanced Features (Week 4-5)
- [ ] **Contact Management**
  - [ ] QR code generation and scanning
  - [ ] Contact storage and organization
  - [ ] Presence tracking (online/offline)
  - [ ] Device identity management

- [ ] **Device Synchronization**
  - [ ] Cross-device pairing mechanism
  - [ ] Message sync across devices
  - [ ] Conflict resolution
  - [ ] Backup and restore functionality

### Phase 4: Polish & Testing (Week 6)
- [ ] **UI/UX Polish**
  - [ ] Responsive design for all screen sizes
  - [ ] Theme support (light/dark/auto)
  - [ ] Accessibility improvements
  - [ ] Performance optimization

- [ ] **Testing & Quality**
  - [ ] Comprehensive unit test suite
  - [ ] Integration tests for P2P flows
  - [ ] End-to-end tests for user workflows
  - [ ] Performance and security testing

**This project structure provides a complete foundation for building AirChat with proper separation of concerns, comprehensive testing, and production-ready configuration.** 