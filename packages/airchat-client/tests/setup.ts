import '@testing-library/jest-dom';

// Mock WebRTC APIs
global.RTCPeerConnection = class MockRTCPeerConnection {
  connectionState: RTCPeerConnectionState = 'new';
  iceConnectionState: RTCIceConnectionState = 'new';
  iceGatheringState: RTCIceGatheringState = 'new';
  localDescription: RTCSessionDescription | null = null;
  remoteDescription: RTCSessionDescription | null = null;

  onconnectionstatechange: ((event: Event) => void) | null = null;
  oniceconnectionstatechange: ((event: Event) => void) | null = null;
  onicegatheringstatechange: ((event: Event) => void) | null = null;
  onicecandidate: ((event: RTCPeerConnectionIceEvent) => void) | null = null;
  onicecandidateerror: ((event: Event) => void) | null = null;
  ondatachannel: ((event: RTCDataChannelEvent) => void) | null = null;

  constructor(config?: RTCConfiguration) {}

  createOffer(): Promise<RTCSessionDescriptionInit> {
    return Promise.resolve({
      type: 'offer',
      sdp: 'mock-offer-sdp',
    });
  }

  createAnswer(): Promise<RTCSessionDescriptionInit> {
    return Promise.resolve({
      type: 'answer',
      sdp: 'mock-answer-sdp',
    });
  }

  setLocalDescription(description: RTCSessionDescriptionInit): Promise<void> {
    this.localDescription = description as RTCSessionDescription;
    return Promise.resolve();
  }

  setRemoteDescription(description: RTCSessionDescriptionInit): Promise<void> {
    this.remoteDescription = description as RTCSessionDescription;
    return Promise.resolve();
  }

  addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    return Promise.resolve();
  }

  createDataChannel(
    label: string,
    options?: RTCDataChannelInit
  ): RTCDataChannel {
    return new MockRTCDataChannel(label, options);
  }

  close(): void {
    this.connectionState = 'closed';
  }

  addEventListener(type: string, listener: EventListener): void {}
  removeEventListener(type: string, listener: EventListener): void {}
  dispatchEvent(event: Event): boolean {
    return true;
  }
};

class MockRTCDataChannel implements RTCDataChannel {
  label: string;
  readyState: RTCDataChannelState = 'connecting';
  bufferedAmount = 0;
  bufferedAmountLowThreshold = 0;
  binaryType: BinaryType = 'blob';
  maxPacketLifeTime: number | null = null;
  maxRetransmits: number | null = null;
  ordered = true;
  protocol = '';
  id: number | null = null;

  onopen: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onclose: ((event: Event) => void) | null = null;
  onbufferedamountlow: ((event: Event) => void) | null = null;

  constructor(label: string, options?: RTCDataChannelInit) {
    this.label = label;
    if (options) {
      this.ordered = options.ordered ?? true;
      this.maxRetransmits = options.maxRetransmits ?? null;
      this.maxPacketLifeTime = options.maxPacketLifeTime ?? null;
    }

    // Simulate opening
    setTimeout(() => {
      this.readyState = 'open';
      this.onopen?.(new Event('open'));
    }, 10);
  }

  send(data: string | Blob | ArrayBuffer | ArrayBufferView): void {
    if (this.readyState !== 'open') {
      throw new Error('DataChannel is not open');
    }
  }

  close(): void {
    this.readyState = 'closed';
    this.onclose?.(new Event('close'));
  }

  addEventListener(type: string, listener: EventListener): void {}
  removeEventListener(type: string, listener: EventListener): void {}
  dispatchEvent(event: Event): boolean {
    return true;
  }
}

// Mock WebSocket
global.WebSocket = class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = WebSocket.CONNECTING;
  url: string;

  onopen: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;

  constructor(url: string) {
    this.url = url;

    // Simulate connection
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.onopen?.(new Event('open'));
    }, 10);
  }

  send(data: string): void {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
  }

  close(code?: number, reason?: string): void {
    this.readyState = WebSocket.CLOSED;
    this.onclose?.(
      new CloseEvent('close', { code: code || 1000, reason: reason || '' })
    );
  }

  addEventListener(type: string, listener: EventListener): void {}
  removeEventListener(type: string, listener: EventListener): void {}
  dispatchEvent(event: Event): boolean {
    return true;
  }
} as any;

// Mock File API
global.File = class MockFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  private _bits: BlobPart[];

  constructor(
    bits: BlobPart[],
    filename: string,
    options: FilePropertyBag = {}
  ) {
    this.name = filename;
    this.type = options.type || '';
    this.lastModified = options.lastModified || Date.now();
    this._bits = bits;

    // Calculate size
    this.size = bits.reduce((total, bit) => {
      if (typeof bit === 'string') {
        return total + new TextEncoder().encode(bit).length;
      } else if (bit instanceof ArrayBuffer) {
        return total + bit.byteLength;
      } else if (bit instanceof Uint8Array) {
        return total + bit.length;
      }
      return total;
    }, 0);
  }

  async arrayBuffer(): Promise<ArrayBuffer> {
    // Convert bits to ArrayBuffer
    const totalSize = this.size;
    const buffer = new ArrayBuffer(totalSize);
    const view = new Uint8Array(buffer);
    let offset = 0;

    for (const bit of this._bits) {
      if (typeof bit === 'string') {
        const encoded = new TextEncoder().encode(bit);
        view.set(encoded, offset);
        offset += encoded.length;
      } else if (bit instanceof ArrayBuffer) {
        view.set(new Uint8Array(bit), offset);
        offset += bit.byteLength;
      } else if (bit instanceof Uint8Array) {
        view.set(bit, offset);
        offset += bit.length;
      }
    }

    return buffer;
  }

  async text(): Promise<string> {
    const buffer = await this.arrayBuffer();
    return new TextDecoder().decode(buffer);
  }

  slice(start?: number, end?: number): Blob {
    return new MockFile([], this.name) as any;
  }
} as any;

// Mock IndexedDB
const mockIDBRequest = {
  result: null,
  error: null,
  onsuccess: null,
  onerror: null,
  addEventListener: () => {},
  removeEventListener: () => {},
  dispatchEvent: () => true,
};

global.indexedDB = {
  open: () => mockIDBRequest,
  deleteDatabase: () => mockIDBRequest,
  databases: () => Promise.resolve([]),
  cmp: () => 0,
} as any;

// Mock crypto APIs with simple encryption simulation
const encryptedDataStore = new Map<string, ArrayBuffer>();

Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: (arr: any) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
    randomUUID: () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === 'x' ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },
    subtle: {
      generateKey: vi.fn().mockImplementation((algorithm: any) => {
        const keyId = Math.random().toString(36).substring(7);
        const mockKeyPair = {
          publicKey: { type: 'public', algorithm, keyId },
          privateKey: { type: 'private', algorithm, keyId },
        };
        return Promise.resolve(mockKeyPair);
      }),
      importKey: vi.fn().mockImplementation(() => {
        return Promise.resolve({ type: 'public' });
      }),
      exportKey: vi.fn().mockImplementation((format: string, key: any) => {
        if (format === 'raw') {
          // Return a deterministic but different buffer for different keys
          const buffer = new ArrayBuffer(65); // P-256 uncompressed public key size
          const view = new Uint8Array(buffer);
          // Fill with deterministic data based on key type and keyId
          const seed = key?.type === 'private' ? 42 : 123;
          const keyIdSeed = key?.keyId ? key.keyId.charCodeAt(0) : 0;
          for (let i = 0; i < view.length; i++) {
            view[i] = (seed + keyIdSeed + i) % 256;
          }
          return Promise.resolve(buffer);
        } else if (format === 'jwk') {
          const keyIdSeed = key?.keyId ? key.keyId : 'default';
          return Promise.resolve({
            kty: 'EC',
            crv: 'P-256',
            x: `base64url-encoded-x-${keyIdSeed}`,
            y: `base64url-encoded-y-${keyIdSeed}`,
            d: `base64url-encoded-d-${keyIdSeed}`,
          });
        }
        return Promise.resolve(new ArrayBuffer(32));
      }),
      encrypt: vi
        .fn()
        .mockImplementation((algorithm: any, key: any, data: ArrayBuffer) => {
          // Create a unique key for this encryption
          const encryptionKey = `${key.keyId || 'default'}_${Date.now()}_${Math.random()}`;

          // Store the original data
          encryptedDataStore.set(encryptionKey, data);

          // Return "encrypted" data that includes the key
          const keyBytes = new TextEncoder().encode(encryptionKey);
          const encryptedBuffer = new ArrayBuffer(keyBytes.length + 16); // key + some padding
          const view = new Uint8Array(encryptedBuffer);

          // Store the key in the first part
          view.set(keyBytes, 0);

          // Add some random padding
          for (let i = keyBytes.length; i < view.length; i++) {
            view[i] = Math.floor(Math.random() * 256);
          }

          return Promise.resolve(encryptedBuffer);
        }),
      decrypt: vi
        .fn()
        .mockImplementation(
          (algorithm: any, key: any, encryptedData: ArrayBuffer) => {
            try {
              // Extract the encryption key from the encrypted data
              const view = new Uint8Array(encryptedData);
              const keyLength = encryptedData.byteLength - 16; // Subtract padding
              const keyBytes = view.slice(0, keyLength);
              const encryptionKey = new TextDecoder().decode(keyBytes);

              // Retrieve the original data
              const originalData = encryptedDataStore.get(encryptionKey);
              if (originalData) {
                return Promise.resolve(originalData);
              }

              // If not found, return some test data
              const testData = new TextEncoder().encode('decrypted test data');
              return Promise.resolve(testData.buffer);
            } catch (error) {
              // Simulate decryption failure
              return Promise.reject(new Error('Decryption failed'));
            }
          }
        ),
      sign: () => Promise.resolve(new ArrayBuffer(64)),
      verify: () => Promise.resolve(true),
      digest: vi
        .fn()
        .mockImplementation((algorithm: string, data: ArrayBuffer) => {
          // Create a deterministic hash based on input data
          const view = new Uint8Array(data);
          let hash = 0;
          for (let i = 0; i < view.length; i++) {
            hash = ((hash << 5) - hash + view[i]) & 0xffffffff;
          }

          const hashBuffer = new ArrayBuffer(32); // SHA-256 size
          const hashView = new Uint8Array(hashBuffer);

          // Fill with deterministic data based on hash
          for (let i = 0; i < hashView.length; i++) {
            hashView[i] = (hash + i) % 256;
          }

          return Promise.resolve(hashBuffer);
        }),
      deriveBits: () => Promise.resolve(new ArrayBuffer(32)),
      deriveKey: vi.fn().mockImplementation(() => {
        return Promise.resolve({
          type: 'secret',
          algorithm: { name: 'AES-GCM' },
        });
      }),
    },
  },
});

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    length: 0,
    key: vi.fn(),
  };
})();
Object.defineProperty(global, 'localStorage', { value: localStorageMock });

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    onLine: true,
    connection: {
      type: 'wifi',
      effectiveType: '4g',
      downlink: 10,
      rtt: 50,
      saveData: false,
      addEventListener: () => {},
      removeEventListener: () => {},
    },
  },
});

// Mock performance
Object.defineProperty(global, 'performance', {
  value: {
    now: () => Date.now(),
  },
});

// Mock document
Object.defineProperty(global, 'document', {
  value: {
    hidden: false,
    addEventListener: () => {},
    removeEventListener: () => {},
  },
});

// Mock window
Object.defineProperty(global, 'window', {
  value: {
    addEventListener: () => {},
    removeEventListener: () => {},
    setInterval: global.setInterval,
    clearInterval: global.clearInterval,
    setTimeout: global.setTimeout,
    clearTimeout: global.clearTimeout,
  },
});
