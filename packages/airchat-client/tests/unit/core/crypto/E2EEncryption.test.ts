import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  E2EEncryption,
  DeviceIdentity,
} from '../../../../src/core/crypto/E2EEncryption';

describe('E2EEncryption', () => {
  let encryption1: E2EEncryption;
  let encryption2: E2EEncryption;
  let identity1: DeviceIdentity;
  let identity2: DeviceIdentity;

  beforeEach(async () => {
    encryption1 = new E2EEncryption();
    encryption2 = new E2EEncryption();

    identity1 = await encryption1.initialize();
    identity2 = await encryption2.initialize();
  });

  afterEach(() => {
    encryption1.destroy();
    encryption2.destroy();
  });

  describe('Initialization', () => {
    it('should generate unique device identities', async () => {
      expect(identity1.deviceId).toBeDefined();
      expect(identity2.deviceId).toBeDefined();
      expect(identity1.deviceId).not.toBe(identity2.deviceId);

      expect(identity1.publicKey).toBeInstanceOf(Uint8Array);
      expect(identity2.publicKey).toBeInstanceOf(Uint8Array);
      expect(identity1.publicKey).not.toEqual(identity2.publicKey);
    });

    it('should emit identity-generated event', async () => {
      const mockListener = vi.fn();
      const newEncryption = new E2EEncryption();

      newEncryption.addEventListener('identity-generated', mockListener);
      await newEncryption.initialize();

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: expect.objectContaining({
            deviceId: expect.any(String),
          }),
        })
      );

      newEncryption.destroy();
    });

    it('should return same identity on multiple calls', async () => {
      const identity1a = await encryption1.getDeviceIdentity();
      const identity1b = await encryption1.getDeviceIdentity();

      expect(identity1a.deviceId).toBe(identity1b.deviceId);
      expect(identity1a.publicKey).toEqual(identity1b.publicKey);
    });
  });

  describe('Key Derivation', () => {
    it('should derive shared keys between devices', async () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();

      encryption1.addEventListener('shared-key-derived', mockListener1);
      encryption2.addEventListener('shared-key-derived', mockListener2);

      await encryption1.deriveSharedKey(
        identity2.deviceId,
        identity2.publicKey
      );
      await encryption2.deriveSharedKey(
        identity1.deviceId,
        identity1.publicKey
      );

      expect(mockListener1).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { contactDeviceId: identity2.deviceId },
        })
      );

      expect(mockListener2).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { contactDeviceId: identity1.deviceId },
        })
      );
    });

    it('should throw error for invalid public key', async () => {
      const invalidKey = new Uint8Array(32); // Wrong size/format

      // Mock the importKey to fail for invalid keys
      const originalImportKey = crypto.subtle.importKey;
      crypto.subtle.importKey = vi
        .fn()
        .mockRejectedValue(new Error('Invalid key format'));

      await expect(
        encryption1.deriveSharedKey('invalid-device', invalidKey)
      ).rejects.toThrow('Key derivation failed');

      // Restore original mock
      crypto.subtle.importKey = originalImportKey;
    });
  });

  describe('Message Encryption/Decryption', () => {
    beforeEach(async () => {
      // Establish shared keys
      await encryption1.deriveSharedKey(
        identity2.deviceId,
        identity2.publicKey
      );
      await encryption2.deriveSharedKey(
        identity1.deviceId,
        identity1.publicKey
      );
    });

    it('should encrypt and decrypt messages correctly', async () => {
      const originalMessage = 'Hello, this is a secret message!';

      // Encrypt with device 1
      const encryptedMessage = await encryption1.encryptMessage(
        identity2.deviceId,
        originalMessage
      );

      expect(encryptedMessage).toMatchObject({
        id: expect.any(String),
        from: identity1.deviceId,
        to: identity2.deviceId,
        encryptedContent: expect.any(String),
        iv: expect.any(String),
        timestamp: expect.any(Number),
        keyFingerprint: expect.any(String),
      });

      // Decrypt with device 2
      const decryptedMessage =
        await encryption2.decryptMessage(encryptedMessage);

      expect(decryptedMessage).toBe(originalMessage);
    });

    it('should generate unique IVs for each message', async () => {
      const message = 'Test message';

      const encrypted1 = await encryption1.encryptMessage(
        identity2.deviceId,
        message
      );
      const encrypted2 = await encryption1.encryptMessage(
        identity2.deviceId,
        message
      );

      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      expect(encrypted1.encryptedContent).not.toBe(encrypted2.encryptedContent);
    });

    it('should throw error when no shared key exists', async () => {
      await expect(
        encryption1.encryptMessage('unknown-device', 'test')
      ).rejects.toThrow('No shared key for contact unknown-device');
    });

    it('should throw error for tampered messages', async () => {
      const originalMessage = 'Original message';
      const encryptedMessage = await encryption1.encryptMessage(
        identity2.deviceId,
        originalMessage
      );

      // Tamper with the encrypted content
      encryptedMessage.encryptedContent =
        encryptedMessage.encryptedContent.slice(0, -1) + 'X';

      await expect(
        encryption2.decryptMessage(encryptedMessage)
      ).rejects.toThrow('Message decryption failed');
    });

    it('should verify key fingerprints', async () => {
      const message = 'Test message';
      const encryptedMessage = await encryption1.encryptMessage(
        identity2.deviceId,
        message
      );

      // Tamper with key fingerprint
      encryptedMessage.keyFingerprint = 'invalid';

      await expect(
        encryption2.decryptMessage(encryptedMessage)
      ).rejects.toThrow('Key fingerprint mismatch');
    });
  });

  describe('File Encryption', () => {
    beforeEach(async () => {
      // Establish shared keys
      await encryption1.deriveSharedKey(
        identity2.deviceId,
        identity2.publicKey
      );
    });

    it('should encrypt files with chunking', async () => {
      const fileContent = new Uint8Array(1024 * 1024); // 1MB file
      fileContent.fill(42); // Fill with test data

      const file = new File([fileContent], 'test.bin', {
        type: 'application/octet-stream',
      });

      const encryptedFile = await encryption1.encryptFile(
        identity2.deviceId,
        file
      );

      expect(encryptedFile).toMatchObject({
        id: expect.any(String),
        fileName: 'test.bin',
        fileSize: 1024 * 1024,
        encryptedKey: expect.any(Uint8Array),
        keyIv: expect.any(Uint8Array),
        chunks: expect.any(Array),
        checksum: expect.any(String),
        timestamp: expect.any(Number),
      });

      expect(encryptedFile.chunks.length).toBeGreaterThan(0);
      expect(encryptedFile.chunks[0]).toMatchObject({
        index: 0,
        iv: expect.any(Uint8Array),
        data: expect.any(Uint8Array),
        checksum: expect.any(String),
      });
    });

    it('should handle small files', async () => {
      const fileContent = 'Small file content';
      const file = new File([fileContent], 'small.txt', { type: 'text/plain' });

      const encryptedFile = await encryption1.encryptFile(
        identity2.deviceId,
        file
      );

      expect(encryptedFile.chunks).toHaveLength(1);
      expect(encryptedFile.fileSize).toBe(
        new TextEncoder().encode(fileContent).length
      );
    });

    it('should throw error when no shared key exists for file encryption', async () => {
      const file = new File(['test'], 'test.txt');

      await expect(
        encryption1.encryptFile('unknown-device', file)
      ).rejects.toThrow('No shared key for contact unknown-device');
    });
  });

  describe('Key Rotation', () => {
    beforeEach(async () => {
      await encryption1.deriveSharedKey(
        identity2.deviceId,
        identity2.publicKey
      );
    });

    it('should emit key rotation events', async () => {
      const mockListener = vi.fn();
      encryption1.addEventListener('key-rotation-needed', mockListener);

      // Manually trigger key rotation (normally happens on timer)
      encryption1['rotateKey'](identity2.deviceId);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { contactDeviceId: identity2.deviceId },
        })
      );
    });
  });

  describe('Utility Functions', () => {
    it('should convert between ArrayBuffer and base64', async () => {
      const originalData = new Uint8Array([1, 2, 3, 4, 5]);

      // Access private methods via bracket notation for testing
      const base64 = encryption1['arrayBufferToBase64'](originalData.buffer);
      const converted = encryption1['base64ToArrayBuffer'](base64);

      expect(new Uint8Array(converted)).toEqual(originalData);
    });

    it('should chunk files correctly', async () => {
      const fileContent = new Uint8Array(10000); // 10KB
      fileContent.fill(123);

      const chunks = await encryption1['chunkFile'](
        new File([fileContent], 'test.bin'),
        4096 // 4KB chunks
      );

      expect(chunks).toHaveLength(3); // 4KB + 4KB + 2KB
      expect(chunks[0]).toHaveLength(4096);
      expect(chunks[1]).toHaveLength(4096);
      expect(chunks[2]).toHaveLength(1808); // Remaining bytes
    });

    it('should calculate consistent checksums', async () => {
      const data1 = new Uint8Array([1, 2, 3, 4, 5]);
      const data2 = new Uint8Array([1, 2, 3, 4, 5]);
      const data3 = new Uint8Array([1, 2, 3, 4, 6]);

      const checksum1 = await encryption1['calculateChecksum'](data1);
      const checksum2 = await encryption1['calculateChecksum'](data2);
      const checksum3 = await encryption1['calculateChecksum'](data3);

      expect(checksum1).toBe(checksum2);
      expect(checksum1).not.toBe(checksum3);
      expect(checksum1).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex
    });
  });

  describe('Error Handling', () => {
    it('should handle encryption errors gracefully', async () => {
      // Try to encrypt without initialization
      const newEncryption = new E2EEncryption();

      await expect(newEncryption.getDeviceIdentity()).rejects.toThrow(
        'Encryption not initialized'
      );

      newEncryption.destroy();
    });

    it('should handle decryption of invalid messages', async () => {
      await encryption1.deriveSharedKey(
        identity2.deviceId,
        identity2.publicKey
      );
      await encryption2.deriveSharedKey(
        identity1.deviceId,
        identity1.publicKey
      );

      const invalidMessage = {
        id: 'test',
        from: identity1.deviceId,
        to: identity2.deviceId,
        encryptedContent: 'invalid-base64-!@#$',
        iv: 'invalid-iv',
        timestamp: Date.now(),
        keyFingerprint: 'invalid',
      };

      await expect(
        encryption2.decryptMessage(invalidMessage)
      ).rejects.toThrow();
    });
  });

  describe('Resource Cleanup', () => {
    it('should clean up resources on destroy', () => {
      const testEncryption = new E2EEncryption();

      // Add some test data
      testEncryption['sharedKeys'].set('test', {} as CryptoKey);
      testEncryption['keyRotationTimers'].set(
        'test',
        setTimeout(() => {}, 1000)
      );

      expect(testEncryption['sharedKeys'].size).toBe(1);
      expect(testEncryption['keyRotationTimers'].size).toBe(1);

      testEncryption.destroy();

      expect(testEncryption['sharedKeys'].size).toBe(0);
      expect(testEncryption['keyRotationTimers'].size).toBe(0);
      expect(testEncryption['keyPair']).toBeNull();
      expect(testEncryption['deviceId']).toBeNull();
    });
  });
});
