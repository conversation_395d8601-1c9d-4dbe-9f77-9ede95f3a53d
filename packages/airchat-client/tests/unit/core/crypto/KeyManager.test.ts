import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  KeyManager,
  ContactKey,
  StoredKeyPair,
} from '../../../../src/core/crypto/KeyManager';
import { DeviceIdentity } from '../../../../src/core/crypto/E2EEncryption';

describe('KeyManager', () => {
  let keyManager: KeyManager;
  let mockDeviceIdentity: DeviceIdentity;
  let mockContactKey: Uint8Array;

  beforeEach(async () => {
    keyManager = new KeyManager();

    // Clear localStorage
    localStorage.clear();

    // Create mock device identity
    const keyPair = await crypto.subtle.generateKey(
      { name: 'ECDH', namedCurve: 'P-256' },
      true,
      ['deriveKey']
    );

    const publicKeyBuffer = await crypto.subtle.exportKey(
      'raw',
      keyPair.publicKey
    );

    mockDeviceIdentity = {
      deviceId: 'test-device-123',
      publicKey: new Uint8Array(publicKeyBuffer),
      privateKey: keyPair.privateKey,
      createdAt: Date.now(),
    };

    // Create mock contact key
    const contactKeyPair = await crypto.subtle.generateKey(
      { name: 'ECDH', namedCurve: 'P-256' },
      true,
      ['deriveKey']
    );
    const contactPublicKeyBuffer = await crypto.subtle.exportKey(
      'raw',
      contactKeyPair.publicKey
    );
    mockContactKey = new Uint8Array(contactPublicKeyBuffer);

    await keyManager.initialize();
  });

  afterEach(() => {
    keyManager.destroy();
    localStorage.clear();
  });

  describe('Device Identity Management', () => {
    it('should store and load device identity', async () => {
      const mockListener = vi.fn();
      keyManager.addEventListener('device-identity-stored', mockListener);

      await keyManager.storeDeviceIdentity(mockDeviceIdentity);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { deviceId: mockDeviceIdentity.deviceId },
        })
      );

      const loadedIdentity = await keyManager.loadDeviceIdentity();

      expect(loadedIdentity).toBeDefined();
      expect(loadedIdentity!.deviceId).toBe(mockDeviceIdentity.deviceId);
      expect(loadedIdentity!.publicKey).toEqual(mockDeviceIdentity.publicKey);
      expect(loadedIdentity!.createdAt).toBe(mockDeviceIdentity.createdAt);
      expect(loadedIdentity!.privateKey).toBeDefined();
    });

    it('should return null when no device identity exists', async () => {
      const identity = await keyManager.loadDeviceIdentity();
      expect(identity).toBeNull();
    });

    it('should handle corrupted device identity data', async () => {
      // Store corrupted data
      localStorage.setItem('airchat_device_identity', 'invalid-json');

      const identity = await keyManager.loadDeviceIdentity();
      expect(identity).toBeNull();

      // Should clear corrupted data
      expect(localStorage.getItem('airchat_device_identity')).toBeNull();
    });

    it('should get current device identity', async () => {
      await keyManager.storeDeviceIdentity(mockDeviceIdentity);

      const identity = keyManager.getDeviceIdentity();
      expect(identity).toBeDefined();
      expect(identity!.deviceId).toBe(mockDeviceIdentity.deviceId);
    });

    it('should throw error when storing identity without private key', async () => {
      const identityWithoutPrivateKey = {
        ...mockDeviceIdentity,
        privateKey: undefined,
      };

      await expect(
        keyManager.storeDeviceIdentity(identityWithoutPrivateKey)
      ).rejects.toThrow('Private key is required for storage');
    });
  });

  describe('Contact Key Management', () => {
    it('should add contact key', async () => {
      const mockListener = vi.fn();
      keyManager.addEventListener('contact-key-added', mockListener);

      await keyManager.addContactKey('contact-123', mockContactKey, false);

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            deviceId: 'contact-123',
            fingerprint: expect.any(String),
            verified: false,
          },
        })
      );

      const contactKey = keyManager.getContactKey('contact-123');
      expect(contactKey).toBeDefined();
      expect(contactKey!.deviceId).toBe('contact-123');
      expect(contactKey!.publicKey).toEqual(mockContactKey);
      expect(contactKey!.verified).toBe(false);
      expect(contactKey!.fingerprint).toBeDefined();
    });

    it('should verify contact key', async () => {
      const mockListener = vi.fn();
      keyManager.addEventListener('contact-key-verified', mockListener);

      await keyManager.addContactKey('contact-123', mockContactKey, false);
      await keyManager.verifyContactKey('contact-123');

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            deviceId: 'contact-123',
            fingerprint: expect.any(String),
          },
        })
      );

      const contactKey = keyManager.getContactKey('contact-123');
      expect(contactKey!.verified).toBe(true);
    });

    it('should remove contact key', async () => {
      const mockListener = vi.fn();
      keyManager.addEventListener('contact-key-removed', mockListener);

      await keyManager.addContactKey('contact-123', mockContactKey);
      await keyManager.removeContactKey('contact-123');

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { deviceId: 'contact-123' },
        })
      );

      const contactKey = keyManager.getContactKey('contact-123');
      expect(contactKey).toBeNull();
    });

    it('should get all contact keys', async () => {
      await keyManager.addContactKey('contact-1', mockContactKey);
      await keyManager.addContactKey('contact-2', mockContactKey);

      const allContacts = keyManager.getAllContactKeys();
      expect(allContacts).toHaveLength(2);
      expect(allContacts.map(c => c.deviceId)).toContain('contact-1');
      expect(allContacts.map(c => c.deviceId)).toContain('contact-2');
    });

    it('should throw error when verifying non-existent contact', async () => {
      await expect(keyManager.verifyContactKey('non-existent')).rejects.toThrow(
        'Contact key not found'
      );
    });

    it('should persist and load contact keys', async () => {
      await keyManager.addContactKey('contact-123', mockContactKey, true);

      // Create new key manager instance
      const newKeyManager = new KeyManager();
      await newKeyManager.initialize();

      const loadedContact = newKeyManager.getContactKey('contact-123');
      expect(loadedContact).toBeDefined();
      expect(loadedContact!.deviceId).toBe('contact-123');
      expect(loadedContact!.verified).toBe(true);

      newKeyManager.destroy();
    });
  });

  describe('Key Backup and Restore', () => {
    beforeEach(async () => {
      await keyManager.storeDeviceIdentity(mockDeviceIdentity);
    });

    it('should create encrypted key backup', async () => {
      const password = 'test-password-123';
      const backup = await keyManager.createKeyBackup(password);

      expect(backup).toMatchObject({
        version: 1,
        deviceId: mockDeviceIdentity.deviceId,
        encryptedPrivateKey: expect.any(String),
        salt: expect.any(Uint8Array),
        iv: expect.any(Uint8Array),
        iterations: expect.any(Number),
        createdAt: expect.any(Number),
      });

      expect(backup.iterations).toBeGreaterThan(50000); // Strong key derivation
    });

    it('should restore from encrypted backup', async () => {
      const password = 'test-password-123';
      const backup = await keyManager.createKeyBackup(password);

      // Clear current identity
      keyManager.destroy();
      localStorage.clear();

      // Create new key manager and restore
      const newKeyManager = new KeyManager();
      await newKeyManager.initialize();

      const mockListener = vi.fn();
      newKeyManager.addEventListener('keys-restored', mockListener);

      const restoredIdentity = await newKeyManager.restoreFromBackup(
        backup,
        password
      );

      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { deviceId: backup.deviceId },
        })
      );

      expect(restoredIdentity.deviceId).toBe(mockDeviceIdentity.deviceId);

      newKeyManager.destroy();
    });

    it('should fail to restore with wrong password', async () => {
      const backup = await keyManager.createKeyBackup('correct-password');

      await expect(
        keyManager.restoreFromBackup(backup, 'wrong-password')
      ).rejects.toThrow('Key restoration failed');
    });

    it('should throw error when creating backup without device identity', async () => {
      // Clear localStorage to ensure clean state
      localStorage.clear();

      const emptyKeyManager = new KeyManager();
      await emptyKeyManager.initialize();

      // Ensure no device identity is loaded
      expect(emptyKeyManager.getDeviceIdentity()).toBeNull();

      await expect(emptyKeyManager.createKeyBackup('password')).rejects.toThrow(
        'No device identity to backup'
      );

      emptyKeyManager.destroy();
    });
  });

  describe('Fingerprint Generation', () => {
    it('should generate consistent fingerprints', async () => {
      const fingerprint1 = await keyManager.generateFingerprint(mockContactKey);
      const fingerprint2 = await keyManager.generateFingerprint(mockContactKey);

      expect(fingerprint1).toBe(fingerprint2);
      expect(fingerprint1).toMatch(/^[a-f0-9 ]+$/); // Hex with spaces
      expect(fingerprint1.length).toBeGreaterThan(10);
    });

    it('should generate different fingerprints for different keys', async () => {
      const keyPair2 = await crypto.subtle.generateKey(
        { name: 'ECDH', namedCurve: 'P-256' },
        true,
        ['deriveKey']
      );
      const publicKey2Buffer = await crypto.subtle.exportKey(
        'raw',
        keyPair2.publicKey
      );
      const mockContactKey2 = new Uint8Array(publicKey2Buffer);

      const fingerprint1 = await keyManager.generateFingerprint(mockContactKey);
      const fingerprint2 =
        await keyManager.generateFingerprint(mockContactKey2);

      expect(fingerprint1).not.toBe(fingerprint2);
    });
  });

  describe('Data Serialization', () => {
    it('should serialize and deserialize Uint8Array correctly', async () => {
      await keyManager.addContactKey('test-contact', mockContactKey);

      // Force save and reload
      await keyManager['saveContactKeys']();

      const newKeyManager = new KeyManager();
      await newKeyManager.initialize();

      const loadedContact = newKeyManager.getContactKey('test-contact');
      expect(loadedContact).toBeDefined();
      expect(loadedContact!.publicKey).toEqual(mockContactKey);
      expect(loadedContact!.publicKey).toBeInstanceOf(Uint8Array);

      newKeyManager.destroy();
    });

    it('should handle corrupted contact data', async () => {
      // Store corrupted contact data
      localStorage.setItem('airchat_contact_keys', 'invalid-json');

      const newKeyManager = new KeyManager();
      await newKeyManager.initialize();

      const contacts = newKeyManager.getAllContactKeys();
      expect(contacts).toHaveLength(0);

      // Should clear corrupted data
      expect(localStorage.getItem('airchat_contact_keys')).toBeNull();

      newKeyManager.destroy();
    });
  });

  describe('Event Handling', () => {
    it('should emit events for key operations', async () => {
      const addedListener = vi.fn();
      const verifiedListener = vi.fn();
      const removedListener = vi.fn();

      keyManager.addEventListener('contact-key-added', addedListener);
      keyManager.addEventListener('contact-key-verified', verifiedListener);
      keyManager.addEventListener('contact-key-removed', removedListener);

      await keyManager.addContactKey('test-contact', mockContactKey);
      await keyManager.verifyContactKey('test-contact');
      await keyManager.removeContactKey('test-contact');

      expect(addedListener).toHaveBeenCalledTimes(1);
      expect(verifiedListener).toHaveBeenCalledTimes(1);
      expect(removedListener).toHaveBeenCalledTimes(1);
    });
  });

  describe('Resource Cleanup', () => {
    it('should clean up resources on destroy', async () => {
      await keyManager.storeDeviceIdentity(mockDeviceIdentity);
      await keyManager.addContactKey('test-contact', mockContactKey);

      expect(keyManager.getDeviceIdentity()).toBeDefined();
      expect(keyManager.getAllContactKeys()).toHaveLength(1);

      keyManager.destroy();

      expect(keyManager.getDeviceIdentity()).toBeNull();
      expect(keyManager.getAllContactKeys()).toHaveLength(0);
    });
  });
});
