import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MessageQueue } from '@/core/messaging/MessageQueue';
import type { Message } from '@airchat/shared';

describe('MessageQueue', () => {
  let messageQueue: MessageQueue;
  let mockSendCallback: ReturnType<typeof vi.fn>;
  let mockEventListener: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockSendCallback = vi.fn().mockResolvedValue(undefined);
    mockEventListener = vi.fn();

    // Clear localStorage before creating queue
    localStorage.clear();

    messageQueue = new MessageQueue(mockSendCallback, {
      maxAttempts: 3,
      baseRetryDelay: 100,
      processingInterval: 50,
      persistenceKey: 'test-message-queue',
    });
  });

  afterEach(() => {
    messageQueue.destroy();
    localStorage.clear();
  });

  const createTestMessage = (id: string = 'test-msg-1'): Message => ({
    id,
    from: 'user1',
    to: 'user2',
    content: { type: 'text', text: 'Hello world' },
    timestamp: Date.now(),
    edited: false,
    reactions: {},
  });

  describe('Message Enqueueing', () => {
    it('should enqueue a message', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message = createTestMessage();
      const to = 'user2';

      messageQueue.addEventListener('message-queued', mockEventListener);

      await messageQueue.enqueue(message, to);

      // Wait for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      const queuedMessages = messageQueue.getQueuedMessages();
      expect(queuedMessages).toHaveLength(1);
      expect(queuedMessages[0].message).toEqual(message);
      expect(queuedMessages[0].to).toBe(to);

      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { messageId: message.id, to },
        })
      );
    });

    it('should respect message priority', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');
      const message3 = createTestMessage('msg3');

      await messageQueue.enqueue(message1, 'user2', 'low');
      await messageQueue.enqueue(message2, 'user2', 'high');
      await messageQueue.enqueue(message3, 'user2', 'normal');

      // Wait a bit for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      const queuedMessages = messageQueue.getQueuedMessages();
      expect(queuedMessages).toHaveLength(3);
      expect(queuedMessages[0].message.id).toBe('msg2'); // high priority first
      expect(queuedMessages[1].message.id).toBe('msg3'); // normal priority second
      expect(queuedMessages[2].message.id).toBe('msg1'); // low priority last
    });

    it('should persist queue to localStorage', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message = createTestMessage();

      await messageQueue.enqueue(message, 'user2');

      // Wait for persistence
      await new Promise(resolve => setTimeout(resolve, 50));

      const stored = localStorage.getItem('test-message-queue');
      expect(stored).toBeTruthy();

      const parsed = JSON.parse(stored!);
      expect(parsed).toHaveLength(1);
      expect(parsed[0][1].message.id).toBe(message.id);
    });
  });

  describe('Message Processing', () => {
    it('should process queued messages successfully', async () => {
      const message = createTestMessage();

      messageQueue.addEventListener('message-sent', mockEventListener);

      await messageQueue.enqueue(message, 'user2');

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockSendCallback).toHaveBeenCalledWith(message, 'user2');
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { messageId: message.id, to: 'user2' },
        })
      );

      // Message should be removed from queue after successful send
      expect(messageQueue.getQueuedMessages()).toHaveLength(0);
    });

    it('should retry failed messages', async () => {
      const message = createTestMessage();
      const error = new Error('Send failed');

      mockSendCallback
        .mockRejectedValueOnce(error)
        .mockResolvedValue(undefined);

      await messageQueue.enqueue(message, 'user2');

      // Wait for initial attempt and retry
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(mockSendCallback).toHaveBeenCalledTimes(2);
      expect(messageQueue.getQueuedMessages()).toHaveLength(0);
    });

    it('should mark messages as failed after max attempts', async () => {
      const message = createTestMessage();
      const error = new Error('Send failed');

      mockSendCallback.mockRejectedValue(error);
      messageQueue.addEventListener('message-failed', mockEventListener);

      await messageQueue.enqueue(message, 'user2');

      // Wait for all retry attempts
      await new Promise(resolve => setTimeout(resolve, 500));

      expect(mockSendCallback).toHaveBeenCalledTimes(3); // maxAttempts
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { messageId: message.id, to: 'user2', error: 'Send failed' },
        })
      );

      const failedMessages = messageQueue.getFailedMessages();
      expect(failedMessages).toHaveLength(1);
      expect(failedMessages[0].message.id).toBe(message.id);
    });

    it('should emit queue-processed events', async () => {
      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');

      messageQueue.addEventListener('queue-processed', mockEventListener);

      await messageQueue.enqueue(message1, 'user2');
      await messageQueue.enqueue(message2, 'user2');

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockEventListener).toHaveBeenCalled();

      // Check that at least one processing event occurred
      const calls = mockEventListener.mock.calls;
      const hasProcessingEvent = calls.some(call => {
        const event = call[0];
        return (
          event instanceof CustomEvent &&
          event.detail &&
          typeof event.detail.sent === 'number'
        );
      });

      expect(hasProcessingEvent).toBe(true);
    });
  });

  describe('Queue Management', () => {
    it('should dequeue specific messages', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');

      await messageQueue.enqueue(message1, 'user2');
      await messageQueue.enqueue(message2, 'user2');

      // Wait for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(messageQueue.getQueuedMessages()).toHaveLength(2);

      const removed = await messageQueue.dequeue('msg1');
      expect(removed).toBe(true);
      expect(messageQueue.getQueuedMessages()).toHaveLength(1);
      expect(messageQueue.getQueuedMessages()[0].message.id).toBe('msg2');
    });

    it('should retry specific messages', async () => {
      const message = createTestMessage();
      const error = new Error('Send failed');

      mockSendCallback.mockRejectedValue(error);

      await messageQueue.enqueue(message, 'user2');

      // Wait for failure
      await new Promise(resolve => setTimeout(resolve, 200));

      // Reset mock to succeed
      mockSendCallback.mockResolvedValue(undefined);

      await messageQueue.retry(message.id);

      // Wait for retry
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(messageQueue.getQueuedMessages()).toHaveLength(0);
    });

    it('should clear all messages', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');

      await messageQueue.enqueue(message1, 'user2');
      await messageQueue.enqueue(message2, 'user2');

      // Wait for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(messageQueue.getQueuedMessages()).toHaveLength(2);

      await messageQueue.clear();

      expect(messageQueue.getQueuedMessages()).toHaveLength(0);
    });

    it('should clear only failed messages', async () => {
      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');
      const error = new Error('Send failed');

      // Make both messages fail all attempts
      mockSendCallback.mockRejectedValue(error);

      await messageQueue.enqueue(message1, 'user2');
      await messageQueue.enqueue(message2, 'user2');

      // Wait for all retry attempts to complete (3 attempts each with delays)
      await new Promise(resolve => setTimeout(resolve, 800));

      const failedMessages = messageQueue.getFailedMessages();
      expect(failedMessages.length).toBeGreaterThan(0);

      await messageQueue.clearFailed();

      expect(messageQueue.getFailedMessages()).toHaveLength(0);
    });
  });

  describe('Queue Statistics', () => {
    it('should provide accurate queue stats', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message1 = createTestMessage('msg1');
      const message2 = createTestMessage('msg2');
      const message3 = createTestMessage('msg3');

      await messageQueue.enqueue(message1, 'user2', 'high');
      await messageQueue.enqueue(message2, 'user2', 'normal');
      await messageQueue.enqueue(message3, 'user2', 'low');

      // Wait for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      const stats = messageQueue.getQueueStats();

      expect(stats.total).toBe(3);
      expect(stats.pending).toBe(3);
      expect(stats.failed).toBe(0);
      expect(stats.highPriority).toBe(1);
      expect(stats.normalPriority).toBe(1);
      expect(stats.lowPriority).toBe(1);
    });

    it('should track failed messages in stats', async () => {
      const message = createTestMessage();
      const error = new Error('Send failed');

      mockSendCallback.mockRejectedValue(error);

      await messageQueue.enqueue(message, 'user2');

      // Wait for all attempts to fail
      await new Promise(resolve => setTimeout(resolve, 500));

      const stats = messageQueue.getQueueStats();

      expect(stats.total).toBe(1);
      expect(stats.pending).toBe(0);
      expect(stats.failed).toBe(1);
    });
  });

  describe('Persistence', () => {
    it('should load queue from localStorage on creation', async () => {
      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const message = createTestMessage();

      // Enqueue and destroy
      await messageQueue.enqueue(message, 'user2');

      // Wait for persistence
      await new Promise(resolve => setTimeout(resolve, 100));

      messageQueue.destroy();

      // Create new queue instance
      const newQueue = new MessageQueue(mockSendCallback, {
        persistenceKey: 'test-message-queue',
      });

      expect(newQueue.getQueuedMessages()).toHaveLength(1);
      expect(newQueue.getQueuedMessages()[0].message.id).toBe(message.id);

      newQueue.destroy();
    });

    it('should clean up old messages on load', async () => {
      const oldMessage = createTestMessage('old-msg');
      const recentMessage = createTestMessage('recent-msg');

      // Manually create old data
      const oldData = [
        [
          'old-msg',
          {
            id: 'old-msg',
            message: oldMessage,
            to: 'user2',
            attempts: 0,
            maxAttempts: 3,
            nextRetry: Date.now(),
            priority: 'normal',
            createdAt: Date.now() - 25 * 60 * 60 * 1000, // 25 hours ago
          },
        ],
        [
          'recent-msg',
          {
            id: 'recent-msg',
            message: recentMessage,
            to: 'user2',
            attempts: 0,
            maxAttempts: 3,
            nextRetry: Date.now(),
            priority: 'normal',
            createdAt: Date.now(),
          },
        ],
      ];

      localStorage.setItem('test-message-queue', JSON.stringify(oldData));

      // Make send callback fail to prevent immediate processing
      mockSendCallback.mockRejectedValue(new Error('Network error'));

      const newQueue = new MessageQueue(mockSendCallback, {
        persistenceKey: 'test-message-queue',
      });

      // Wait for any processing attempts
      await new Promise(resolve => setTimeout(resolve, 100));

      // Should only have recent message
      expect(newQueue.getQueuedMessages()).toHaveLength(1);
      expect(newQueue.getQueuedMessages()[0].message.id).toBe('recent-msg');

      newQueue.destroy();
    });
  });
});
