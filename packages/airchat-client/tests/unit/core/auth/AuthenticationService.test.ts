import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AuthenticationService } from '../../../../src/core/auth/AuthenticationService';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

// Mock fetch
global.fetch = vi.fn();

// Mock crypto
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'mock-uuid-123'),
  },
  writable: true,
});

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    platform: 'MacIntel',
  },
  writable: true,
});

describe('AuthenticationService', () => {
  let authService: AuthenticationService;
  const mockSignalingUrl = 'https://signaling.example.com';

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    authService = new AuthenticationService(mockSignalingUrl);
  });

  afterEach(() => {
    authService = null as any;
  });

  describe('Initialization', () => {
    it('should initialize with signaling URL', () => {
      expect(authService).toBeDefined();
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
    });

    it('should generate device ID if not exists', () => {
      expect(localStorageMock.getItem).toHaveBeenCalledWith('airchat-device-id');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('airchat-device-id', 'mock-uuid-123');
    });

    it('should use existing device ID if available', () => {
      localStorageMock.getItem.mockReturnValue('existing-device-id');
      const newAuthService = new AuthenticationService(mockSignalingUrl);
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('airchat-device-id');
      expect(localStorageMock.setItem).not.toHaveBeenCalledWith('airchat-device-id', expect.any(String));
    });
  });

  describe('User Registration', () => {
    it('should register new user successfully', async () => {
      const mockResponse = {
        success: true,
        user: {
          id: 'user-123',
          username: 'testuser',
          email: '<EMAIL>',
          displayName: 'Test User',
          createdAt: Date.now(),
        },
        session: {
          token: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 86400000,
        },
        device: {
          id: 'mock-uuid-123',
          name: 'Chrome on MacIntel',
          type: 'web',
          isVerified: true,
        },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const eventSpy = vi.fn();
      authService.addEventListener('user-registered', eventSpy);
      authService.addEventListener('authentication-changed', eventSpy);

      const result = await authService.register({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
      });

      expect(result.success).toBe(true);
      expect(result.user).toEqual(mockResponse.user);
      expect(result.session).toEqual(mockResponse.session);
      expect(result.device).toEqual(mockResponse.device);

      expect(authService.isAuthenticated()).toBe(true);
      expect(authService.getCurrentUser()).toEqual(mockResponse.user);

      expect(eventSpy).toHaveBeenCalledTimes(2);
      expect(localStorageMock.setItem).toHaveBeenCalledWith('airchat-user', JSON.stringify(mockResponse.user));
      expect(localStorageMock.setItem).toHaveBeenCalledWith('airchat-session', JSON.stringify(mockResponse.session));
    });

    it('should handle registration failure', async () => {
      const mockError = { error: 'Username already exists' };

      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve(mockError),
      });

      const result = await authService.register({
        username: 'existinguser',
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Username already exists');
      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should handle network errors during registration', async () => {
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const result = await authService.register({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error during registration');
    });
  });

  describe('User Login', () => {
    it('should login user successfully', async () => {
      const mockResponse = {
        success: true,
        user: {
          id: 'user-123',
          username: 'testuser',
          email: '<EMAIL>',
          displayName: 'Test User',
          lastLoginAt: Date.now(),
        },
        session: {
          token: 'mock-token',
          refreshToken: 'mock-refresh-token',
          expiresAt: Date.now() + 86400000,
        },
        device: {
          id: 'mock-uuid-123',
          name: 'Chrome on MacIntel',
          type: 'web',
          isVerified: true,
        },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const eventSpy = vi.fn();
      authService.addEventListener('user-logged-in', eventSpy);
      authService.addEventListener('authentication-changed', eventSpy);

      const result = await authService.login({
        username: 'testuser',
        password: 'password123',
      });

      expect(result.success).toBe(true);
      expect(result.user).toEqual(mockResponse.user);
      expect(authService.isAuthenticated()).toBe(true);
      expect(eventSpy).toHaveBeenCalledTimes(2);
    });

    it('should handle login failure', async () => {
      const mockError = { error: 'Invalid credentials' };

      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve(mockError),
      });

      const result = await authService.login({
        username: 'wronguser',
        password: 'wrongpassword',
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid credentials');
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('User Logout', () => {
    it('should logout user successfully', async () => {
      // First login
      const mockLoginResponse = {
        user: { id: 'user-123', username: 'testuser' },
        session: { token: 'mock-token', refreshToken: 'mock-refresh', expiresAt: Date.now() + 86400000 },
        device: { id: 'device-123' },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockLoginResponse),
      });

      await authService.login({ username: 'testuser', password: 'password123' });

      // Then logout
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });

      const eventSpy = vi.fn();
      authService.addEventListener('user-logged-out', eventSpy);
      authService.addEventListener('authentication-changed', eventSpy);

      await authService.logout();

      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
      expect(eventSpy).toHaveBeenCalledTimes(2);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('airchat-user');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('airchat-session');
    });

    it('should clear session even if logout request fails', async () => {
      // Setup authenticated state
      (authService as any).currentUser = { id: 'user-123' };
      (authService as any).currentSession = { token: 'mock-token' };

      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      await authService.logout();

      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBeNull();
    });
  });

  describe('Profile Management', () => {
    beforeEach(async () => {
      // Setup authenticated state
      const mockLoginResponse = {
        user: { id: 'user-123', username: 'testuser', displayName: 'Test User' },
        session: { token: 'mock-token', refreshToken: 'mock-refresh', expiresAt: Date.now() + 86400000 },
        device: { id: 'device-123' },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockLoginResponse),
      });

      await authService.login({ username: 'testuser', password: 'password123' });
      vi.clearAllMocks();
    });

    it('should get user profile', async () => {
      const mockProfile = {
        user: {
          id: 'user-123',
          username: 'testuser',
          email: '<EMAIL>',
          displayName: 'Test User',
          preferences: { theme: 'dark' },
        },
        devices: [
          { id: 'device-123', name: 'Test Device', type: 'web' },
        ],
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockProfile),
      });

      const profile = await authService.getProfile();

      expect(profile).toEqual(mockProfile.user);
      expect(global.fetch).toHaveBeenCalledWith(
        `${mockSignalingUrl}/api/profile`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
          }),
        })
      );
    });

    it('should update user profile', async () => {
      const updates = {
        displayName: 'Updated Name',
        preferences: { theme: 'light' as const },
      };

      const mockResponse = {
        success: true,
        user: {
          id: 'user-123',
          username: 'testuser',
          displayName: 'Updated Name',
          preferences: { theme: 'light' },
        },
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const eventSpy = vi.fn();
      authService.addEventListener('profile-updated', eventSpy);

      const success = await authService.updateProfile(updates);

      expect(success).toBe(true);
      expect(authService.getCurrentUser()?.displayName).toBe('Updated Name');
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { user: mockResponse.user },
        })
      );
    });

    it('should handle unauthorized profile requests', async () => {
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({ error: 'Unauthorized' }),
      });

      const eventSpy = vi.fn();
      authService.addEventListener('authentication-changed', eventSpy);

      const profile = await authService.getProfile();

      expect(profile).toBeNull();
      expect(authService.isAuthenticated()).toBe(false);
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { authenticated: false, user: null },
        })
      );
    });
  });

  describe('Session Management', () => {
    it('should load stored session on initialization', () => {
      const mockUser = { id: 'user-123', username: 'testuser' };
      const mockSession = { token: 'stored-token', expiresAt: Date.now() + 86400000 };

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'airchat-user') return JSON.stringify(mockUser);
        if (key === 'airchat-session') return JSON.stringify(mockSession);
        if (key === 'airchat-device-id') return 'stored-device-id';
        return null;
      });

      const eventSpy = vi.fn();
      const newAuthService = new AuthenticationService(mockSignalingUrl);
      newAuthService.addEventListener('authentication-changed', eventSpy);

      expect(newAuthService.isAuthenticated()).toBe(true);
      expect(newAuthService.getCurrentUser()).toEqual(mockUser);
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { authenticated: true, user: mockUser },
        })
      );
    });

    it('should clear expired session on initialization', () => {
      const mockUser = { id: 'user-123', username: 'testuser' };
      const mockSession = { token: 'expired-token', expiresAt: Date.now() - 1000 }; // Expired

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'airchat-user') return JSON.stringify(mockUser);
        if (key === 'airchat-session') return JSON.stringify(mockSession);
        if (key === 'airchat-device-id') return 'stored-device-id';
        return null;
      });

      const newAuthService = new AuthenticationService(mockSignalingUrl);

      expect(newAuthService.isAuthenticated()).toBe(false);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('airchat-user');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('airchat-session');
    });
  });

  describe('Device Information', () => {
    it('should detect mobile device', async () => {
      Object.defineProperty(global, 'navigator', {
        value: {
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
          platform: 'iPhone',
        },
        writable: true,
      });

      const deviceInfo = await (authService as any).getDeviceInfo();

      expect(deviceInfo.type).toBe('mobile');
      expect(deviceInfo.name).toContain('iPhone');
    });

    it('should detect tablet device', async () => {
      Object.defineProperty(global, 'navigator', {
        value: {
          userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
          platform: 'iPad',
        },
        writable: true,
      });

      const deviceInfo = await (authService as any).getDeviceInfo();

      expect(deviceInfo.type).toBe('tablet');
      expect(deviceInfo.name).toContain('iPad');
    });
  });
});
