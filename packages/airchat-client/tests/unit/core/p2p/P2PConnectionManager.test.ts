import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { P2PConnectionManager } from '@/core/p2p/P2PConnectionManager';

describe('P2PConnectionManager', () => {
  let manager: P2PConnectionManager;
  let mockEventListener: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    manager = new P2PConnectionManager({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      maxReconnectAttempts: 3,
      reconnectDelay: 100
    });
    mockEventListener = vi.fn();
  });

  afterEach(() => {
    // Clean up any connections
    const connections = manager.getAllConnections();
    for (const [peerId] of connections) {
      manager.closeConnection(peerId);
    }
  });

  describe('Connection Creation', () => {
    it('should create a new connection for a peer', async () => {
      const peerId = 'test-peer-1';
      
      const connection = await manager.createConnection(peerId, true);
      
      expect(connection).toBeInstanceOf(RTCPeerConnection);
      expect(manager.getConnection(peerId)).toBe(connection);
    });

    it('should create data channels for initiator', async () => {
      const peerId = 'test-peer-1';
      
      await manager.createConnection(peerId, true);
      
      const connectionInfo = manager.getConnectionInfo(peerId);
      expect(connectionInfo).toBeDefined();
      expect(connectionInfo!.dataChannels.size).toBeGreaterThan(0);
    });

    it('should not create data channels for non-initiator', async () => {
      const peerId = 'test-peer-1';
      
      await manager.createConnection(peerId, false);
      
      const connectionInfo = manager.getConnectionInfo(peerId);
      expect(connectionInfo).toBeDefined();
      expect(connectionInfo!.dataChannels.size).toBe(0);
    });

    it('should emit connection-created event', async () => {
      const peerId = 'test-peer-1';
      manager.addEventListener('connection-created', mockEventListener);
      
      await manager.createConnection(peerId, true);
      
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { peerId, isInitiator: true }
        })
      );
    });

    it('should replace existing connection', async () => {
      const peerId = 'test-peer-1';
      
      const connection1 = await manager.createConnection(peerId, true);
      const connection2 = await manager.createConnection(peerId, true);
      
      expect(connection1).not.toBe(connection2);
      expect(manager.getConnection(peerId)).toBe(connection2);
    });
  });

  describe('Message Sending', () => {
    it('should send message when channel is open', async () => {
      const peerId = 'test-peer-1';
      const message = { type: 'test', content: 'hello' };
      
      await manager.createConnection(peerId, true);
      
      // Wait for data channel to open
      await new Promise(resolve => setTimeout(resolve, 20));
      
      await expect(manager.sendMessage(peerId, message)).resolves.not.toThrow();
    });

    it('should queue message when channel is not open', async () => {
      const peerId = 'test-peer-1';
      const message = { type: 'test', content: 'hello' };
      
      await manager.createConnection(peerId, true);
      
      // Don't wait for channel to open
      await expect(manager.sendMessage(peerId, message)).resolves.not.toThrow();
    });

    it('should throw error for non-existent peer', async () => {
      const peerId = 'non-existent-peer';
      const message = { type: 'test', content: 'hello' };
      
      await expect(manager.sendMessage(peerId, message)).rejects.toThrow();
    });

    it('should emit message-sent event', async () => {
      const peerId = 'test-peer-1';
      const message = { type: 'test', content: 'hello' };
      
      manager.addEventListener('message-sent', mockEventListener);
      await manager.createConnection(peerId, true);
      
      // Wait for data channel to open
      await new Promise(resolve => setTimeout(resolve, 20));
      
      await manager.sendMessage(peerId, message);
      
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { peerId, channelType: 'messages', message }
        })
      );
    });
  });

  describe('Connection State Management', () => {
    it('should track connection state', async () => {
      const peerId = 'test-peer-1';
      
      await manager.createConnection(peerId, true);
      
      const connectionInfo = manager.getConnectionInfo(peerId);
      expect(connectionInfo!.state).toBe('connecting');
    });

    it('should detect connected state', async () => {
      const peerId = 'test-peer-1';
      
      await manager.createConnection(peerId, true);
      
      // Initially not connected
      expect(manager.isConnected(peerId)).toBe(false);
      
      // Simulate connection
      const connectionInfo = manager.getConnectionInfo(peerId)!;
      connectionInfo.state = 'connected';
      
      expect(manager.isConnected(peerId)).toBe(true);
    });

    it('should handle connection state changes', async () => {
      const peerId = 'test-peer-1';
      
      manager.addEventListener('connection-state-change', mockEventListener);
      const connection = await manager.createConnection(peerId, true);
      
      // Simulate state change
      connection.connectionState = 'connected';
      connection.onconnectionstatechange?.(new Event('connectionstatechange'));
      
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { peerId, state: 'connected' }
        })
      );
    });
  });

  describe('Connection Cleanup', () => {
    it('should close connection properly', async () => {
      const peerId = 'test-peer-1';
      
      await manager.createConnection(peerId, true);
      expect(manager.getConnection(peerId)).toBeDefined();
      
      await manager.closeConnection(peerId);
      expect(manager.getConnection(peerId)).toBeUndefined();
    });

    it('should emit cleanup event', async () => {
      const peerId = 'test-peer-1';
      
      manager.addEventListener('peer-cleanup', mockEventListener);
      await manager.createConnection(peerId, true);
      
      await manager.closeConnection(peerId);
      
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { peerId }
        })
      );
    });

    it('should handle closing non-existent connection', async () => {
      const peerId = 'non-existent-peer';
      
      await expect(manager.closeConnection(peerId)).resolves.not.toThrow();
    });
  });

  describe('Multiple Connections', () => {
    it('should manage multiple peer connections', async () => {
      const peer1 = 'test-peer-1';
      const peer2 = 'test-peer-2';
      
      await manager.createConnection(peer1, true);
      await manager.createConnection(peer2, true);
      
      expect(manager.getConnection(peer1)).toBeDefined();
      expect(manager.getConnection(peer2)).toBeDefined();
      expect(manager.getAllConnections().size).toBe(2);
    });

    it('should send messages to specific peers', async () => {
      const peer1 = 'test-peer-1';
      const peer2 = 'test-peer-2';
      const message1 = { type: 'test', content: 'hello peer 1' };
      const message2 = { type: 'test', content: 'hello peer 2' };
      
      await manager.createConnection(peer1, true);
      await manager.createConnection(peer2, true);
      
      // Wait for channels to open
      await new Promise(resolve => setTimeout(resolve, 20));
      
      await expect(manager.sendMessage(peer1, message1)).resolves.not.toThrow();
      await expect(manager.sendMessage(peer2, message2)).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle connection creation errors', async () => {
      // Mock RTCPeerConnection to throw error
      const originalRTC = global.RTCPeerConnection;
      global.RTCPeerConnection = class {
        constructor() {
          throw new Error('Connection failed');
        }
      } as any;
      
      const peerId = 'test-peer-1';
      
      await expect(manager.createConnection(peerId, true)).rejects.toThrow('Connection failed');
      
      // Restore original
      global.RTCPeerConnection = originalRTC;
    });

    it('should emit connection-error event on failure', async () => {
      const originalRTC = global.RTCPeerConnection;
      global.RTCPeerConnection = class {
        constructor() {
          throw new Error('Connection failed');
        }
      } as any;
      
      const peerId = 'test-peer-1';
      manager.addEventListener('connection-error', mockEventListener);
      
      try {
        await manager.createConnection(peerId, true);
      } catch (error) {
        // Expected to throw
      }
      
      expect(mockEventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { peerId, error: expect.any(Error) }
        })
      );
      
      global.RTCPeerConnection = originalRTC;
    });
  });
});
