import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConnectionFallbackManager } from '../../../../src/core/p2p/ConnectionFallbackManager';
import type { NetworkConnectivityResult } from '../../../../src/core/p2p/TurnServerManager';

// Mock WebRTC APIs
const mockDataChannel = {
  close: vi.fn(),
  onopen: null,
  onerror: null,
};

const mockPeerConnection = {
  createDataChannel: vi.fn(() => mockDataChannel),
  createOffer: vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
  setLocalDescription: vi.fn().mockResolvedValue(undefined),
  close: vi.fn(),
  connectionState: 'new',
  iceConnectionState: 'new',
  onconnectionstatechange: null,
  oniceconnectionstatechange: null,
};

global.RTCPeerConnection = vi.fn(() => mockPeerConnection) as any;

describe('ConnectionFallbackManager', () => {
  let fallbackManager: ConnectionFallbackManager;

  beforeEach(() => {
    fallbackManager = new ConnectionFallbackManager();
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any resources
  });

  describe('Strategy Management', () => {
    it('should initialize with default strategies', () => {
      const strategies = fallbackManager.getStrategies();

      expect(strategies.length).toBeGreaterThan(0);
      expect(strategies).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ name: 'direct' }),
          expect.objectContaining({ name: 'stun' }),
          expect.objectContaining({ name: 'turn-udp' }),
          expect.objectContaining({ name: 'turn-tcp' }),
          expect.objectContaining({ name: 'turn-tls' }),
          expect.objectContaining({ name: 'websocket-relay' }),
        ])
      );
    });

    it('should add custom strategy', () => {
      const customStrategy = {
        name: 'custom-test',
        priority: 10,
        description: 'Custom test strategy',
        requirements: ['Test requirement'],
        iceServers: [{ urls: 'stun:test.example.com:3478' }],
        timeout: 5000,
        maxRetries: 1,
      };

      const eventSpy = vi.fn();
      fallbackManager.addEventListener('strategy-added', eventSpy);

      fallbackManager.addStrategy(customStrategy);

      const strategies = fallbackManager.getStrategies();
      expect(strategies).toContainEqual(customStrategy);
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { strategy: customStrategy },
        })
      );
    });

    it('should remove strategy', () => {
      const eventSpy = vi.fn();
      fallbackManager.addEventListener('strategy-removed', eventSpy);

      fallbackManager.removeStrategy('direct');

      const strategies = fallbackManager.getStrategies();
      expect(strategies.find(s => s.name === 'direct')).toBeUndefined();
      expect(eventSpy).toHaveBeenCalled();
    });

    it('should sort strategies by priority', () => {
      const strategies = fallbackManager.getStrategies();
      
      for (let i = 1; i < strategies.length; i++) {
        expect(strategies[i].priority).toBeGreaterThanOrEqual(strategies[i - 1].priority);
      }
    });
  });

  describe('TURN Server Updates', () => {
    it('should update TURN servers for TURN strategies', () => {
      const turnServers = [
        {
          urls: 'turn:test.example.com:3478',
          username: 'testuser',
          credential: 'testpass',
          priority: 1,
        },
        {
          urls: 'turns:test.example.com:443',
          username: 'testuser',
          credential: 'testpass',
          priority: 2,
        },
      ];

      const eventSpy = vi.fn();
      fallbackManager.addEventListener('turn-servers-updated', eventSpy);

      fallbackManager.updateTurnServers(turnServers);

      const strategies = fallbackManager.getStrategies();
      const turnUdpStrategy = strategies.find(s => s.name === 'turn-udp');
      const turnTlsStrategy = strategies.find(s => s.name === 'turn-tls');

      expect(turnUdpStrategy?.iceServers.length).toBeGreaterThan(0);
      expect(turnTlsStrategy?.iceServers.length).toBeGreaterThan(0);
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { turnServers },
        })
      );
    });
  });

  describe('Strategy Selection', () => {
    it('should filter strategies based on network conditions', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: false,
        natType: 'symmetric',
        requiresTurn: true,
        stunWorking: false,
        firewallRestrictions: ['UDP traffic blocked'],
        recommendedServers: [],
      };

      // Mock the private method to test strategy filtering
      const applicableStrategies = (fallbackManager as any).getApplicableStrategies(networkConnectivity);

      // Should exclude direct (NAT present) and stun (symmetric NAT)
      expect(applicableStrategies.find((s: any) => s.name === 'direct')).toBeUndefined();
      expect(applicableStrategies.find((s: any) => s.name === 'stun')).toBeUndefined();
      
      // Should exclude UDP TURN (UDP blocked)
      expect(applicableStrategies.find((s: any) => s.name === 'turn-udp')).toBeUndefined();
      
      // Should include TCP and TLS TURN
      expect(applicableStrategies.find((s: any) => s.name === 'turn-tcp')).toBeDefined();
      expect(applicableStrategies.find((s: any) => s.name === 'turn-tls')).toBeDefined();
    });

    it('should prefer TLS TURN for corporate firewalls', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: false,
        natType: 'full-cone',
        requiresTurn: true,
        stunWorking: true,
        firewallRestrictions: ['Corporate firewall detected'],
        recommendedServers: [],
      };

      const applicableStrategies = (fallbackManager as any).getApplicableStrategies(networkConnectivity);

      // Should only include TLS TURN and WebSocket relay for corporate firewalls
      const strategyNames = applicableStrategies.map((s: any) => s.name);
      expect(strategyNames).toContain('turn-tls');
      expect(strategyNames).toContain('websocket-relay');
      expect(strategyNames).not.toContain('turn-udp');
      expect(strategyNames).not.toContain('turn-tcp');
    });
  });

  describe('Connection Attempts', () => {
    it('should attempt connection with fallback strategies', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: true,
        natType: 'none',
        requiresTurn: false,
        stunWorking: true,
        firewallRestrictions: [],
        recommendedServers: [],
      };

      // Mock successful connection
      mockPeerConnection.connectionState = 'connected';
      setTimeout(() => {
        if (mockPeerConnection.onconnectionstatechange) {
          mockPeerConnection.onconnectionstatechange({} as Event);
        }
      }, 100);

      const eventSpy = vi.fn();
      fallbackManager.addEventListener('fallback-success', eventSpy);

      const result = await fallbackManager.attemptConnectionWithFallback(
        'test-peer',
        networkConnectivity,
        true
      );

      expect(result.success).toBe(true);
      expect(result.strategy?.name).toBe('direct');
      expect(result.attempts.length).toBeGreaterThan(0);
      expect(result.finalConnection).toBeDefined();
      expect(eventSpy).toHaveBeenCalled();
    }, 15000);

    it('should try multiple strategies on failure', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: false,
        natType: 'symmetric',
        requiresTurn: true,
        stunWorking: false,
        firewallRestrictions: [],
        recommendedServers: [],
      };

      // Mock all connections failing
      mockPeerConnection.connectionState = 'failed';
      setTimeout(() => {
        if (mockPeerConnection.onconnectionstatechange) {
          mockPeerConnection.onconnectionstatechange({} as Event);
        }
      }, 100);

      const eventSpy = vi.fn();
      fallbackManager.addEventListener('fallback-failed', eventSpy);

      const result = await fallbackManager.attemptConnectionWithFallback(
        'test-peer',
        networkConnectivity,
        true
      );

      expect(result.success).toBe(false);
      expect(result.attempts.length).toBeGreaterThan(1); // Should try multiple strategies
      expect(eventSpy).toHaveBeenCalled();
    }, 20000);

    it('should handle connection timeout', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: true,
        natType: 'none',
        requiresTurn: false,
        stunWorking: true,
        firewallRestrictions: [],
        recommendedServers: [],
      };

      // Don't trigger any connection state changes to simulate timeout
      const result = await fallbackManager.attemptConnectionWithFallback(
        'test-peer',
        networkConnectivity,
        true
      );

      expect(result.success).toBe(false);
      expect(result.attempts[0].strategy.name).toBe('direct');
    }, 15000);
  });

  describe('Attempt History', () => {
    it('should track attempt history', async () => {
      const networkConnectivity: NetworkConnectivityResult = {
        canConnectDirectly: true,
        natType: 'none',
        requiresTurn: false,
        stunWorking: true,
        firewallRestrictions: [],
        recommendedServers: [],
      };

      // Mock failed connection
      mockPeerConnection.connectionState = 'failed';
      setTimeout(() => {
        if (mockPeerConnection.onconnectionstatechange) {
          mockPeerConnection.onconnectionstatechange({} as Event);
        }
      }, 100);

      await fallbackManager.attemptConnectionWithFallback(
        'test-peer',
        networkConnectivity,
        true
      );

      const history = fallbackManager.getAttemptHistory();
      expect(history.length).toBeGreaterThan(0);
      expect(history[0]).toMatchObject({
        strategy: expect.any(Object),
        startTime: expect.any(Number),
        endTime: expect.any(Number),
        success: expect.any(Boolean),
      });
    }, 15000);

    it('should clear attempt history', () => {
      // Add some mock history
      (fallbackManager as any).attemptHistory = [
        { strategy: { name: 'test' }, startTime: Date.now(), success: false },
      ];

      const eventSpy = vi.fn();
      fallbackManager.addEventListener('history-cleared', eventSpy);

      fallbackManager.clearHistory();

      expect(fallbackManager.getAttemptHistory()).toHaveLength(0);
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('Current Attempt Tracking', () => {
    it('should track current attempt', () => {
      expect(fallbackManager.getCurrentAttempt()).toBeNull();

      // Mock an ongoing attempt
      const mockAttempt = {
        strategy: { name: 'test', priority: 1, description: '', requirements: [], iceServers: [], timeout: 5000, maxRetries: 1 },
        startTime: Date.now(),
        success: false,
      };

      (fallbackManager as any).currentAttempt = mockAttempt;

      expect(fallbackManager.getCurrentAttempt()).toBe(mockAttempt);
    });
  });
});
