import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { P2PConnectionManager } from '../../../../src/core/p2p/P2PConnectionManager';
import type { TurnServerProvider } from '../../../../src/core/p2p/TurnServerManager';

// Mock WebRTC APIs
const mockDataChannel = {
  readyState: 'open',
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
};

const mockPeerConnection = {
  createDataChannel: vi.fn(() => mockDataChannel),
  createOffer: vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
  createAnswer: vi.fn().mockResolvedValue({ type: 'answer', sdp: 'mock-sdp' }),
  setLocalDescription: vi.fn().mockResolvedValue(undefined),
  setRemoteDescription: vi.fn().mockResolvedValue(undefined),
  addIceCandidate: vi.fn().mockResolvedValue(undefined),
  close: vi.fn(),
  getStats: vi.fn().mockResolvedValue(new Map()),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  connectionState: 'new',
  iceConnectionState: 'new',
  ondatachannel: null,
  onicecandidate: null,
  onconnectionstatechange: null,
  oniceconnectionstatechange: null,
};

global.RTCPeerConnection = vi.fn(() => mockPeerConnection) as any;

describe('P2PConnectionManager with TURN Integration', () => {
  let connectionManager: P2PConnectionManager;

  beforeEach(() => {
    vi.clearAllMocks();
    connectionManager = new P2PConnectionManager();
  });

  afterEach(() => {
    connectionManager.cleanup();
  });

  describe('TURN Server Configuration', () => {
    it('should initialize with TURN server support', () => {
      const customProvider: TurnServerProvider = {
        name: 'custom-turn',
        servers: [
          {
            urls: 'turn:custom.example.com:3478',
            username: 'testuser',
            credential: 'testpass',
            priority: 1,
          },
        ],
      };

      const managerWithTurn = new P2PConnectionManager({
        turnProviders: [customProvider],
        enableTurnFallback: true,
      });

      expect(managerWithTurn).toBeDefined();
      managerWithTurn.cleanup();
    });

    it('should add TURN provider dynamically', () => {
      const provider: TurnServerProvider = {
        name: 'dynamic-provider',
        servers: [
          {
            urls: 'turn:dynamic.example.com:3478',
            priority: 2,
          },
        ],
      };

      // Should not throw
      connectionManager.addTurnProvider(provider);
    });

    it('should remove TURN provider', () => {
      const provider: TurnServerProvider = {
        name: 'removable-provider',
        servers: [{ urls: 'turn:removable.example.com:3478' }],
      };

      connectionManager.addTurnProvider(provider);

      // Should not throw
      connectionManager.removeTurnProvider('removable-provider');
    });
  });

  describe('Network Connectivity Testing', () => {
    it('should test network connectivity', async () => {
      const eventSpy = vi.fn();
      connectionManager.addEventListener(
        'network-connectivity-tested',
        eventSpy
      );

      await connectionManager.testNetworkConnectivity();

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            result: expect.objectContaining({
              canConnectDirectly: expect.any(Boolean),
              natType: expect.any(String),
              requiresTurn: expect.any(Boolean),
              stunWorking: expect.any(Boolean),
            }),
          },
        })
      );
    }, 15000);

    it('should emit turn-required event when TURN is needed', async () => {
      const eventSpy = vi.fn();
      connectionManager.addEventListener('turn-required', eventSpy);

      // Mock the TURN manager to return that TURN is required
      const mockConnectivityResult = {
        canConnectDirectly: false,
        natType: 'symmetric',
        requiresTurn: true,
        stunWorking: false,
        firewallRestrictions: ['UDP blocked'],
        recommendedServers: [],
      };

      const turnManager = (connectionManager as any).turnManager;
      vi.spyOn(turnManager, 'testNetworkConnectivity').mockResolvedValue(
        mockConnectivityResult
      );

      await connectionManager.testNetworkConnectivity();

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { result: mockConnectivityResult },
        })
      );
    });

    it('should get network connectivity status', () => {
      const status = connectionManager.getNetworkConnectivityStatus();
      // Initially null until first test
      expect(status).toBeNull();
    });
  });

  describe('Dynamic ICE Server Selection', () => {
    it('should create connection with optimal ICE servers', async () => {
      const mockIceServers = [
        { urls: 'stun:stun.l.google.com:19302' },
        {
          urls: 'turn:test.example.com:3478',
          username: 'user',
          credential: 'pass',
        },
      ];

      // Mock the getOptimalIceServers method
      vi.spyOn(
        connectionManager as any,
        'getOptimalIceServers'
      ).mockResolvedValue(mockIceServers);

      const connection = await connectionManager.createConnection(
        'test-peer',
        true
      );

      expect(connection).toBeDefined();
      expect(global.RTCPeerConnection).toHaveBeenCalledWith(
        expect.objectContaining({
          iceServers: mockIceServers,
        })
      );
    });

    it('should fallback to basic STUN on ICE server failure', async () => {
      // Mock getOptimalIceServers to throw error
      vi.spyOn(
        connectionManager as any,
        'getOptimalIceServers'
      ).mockRejectedValue(new Error('ICE server fetch failed'));

      const connection = await connectionManager.createConnection(
        'test-peer',
        true
      );

      expect(connection).toBeDefined();
      // Should still create connection with fallback servers
      expect(global.RTCPeerConnection).toHaveBeenCalledWith(
        expect.objectContaining({
          iceServers: expect.arrayContaining([
            expect.objectContaining({ urls: 'stun:stun.l.google.com:19302' }),
          ]),
        })
      );
    });

    it('should deduplicate ICE servers', () => {
      const duplicateServers = [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun.l.google.com:19302' }, // Duplicate
        { urls: 'turn:test.com:3478', username: 'user1', credential: 'pass1' },
        { urls: 'turn:test.com:3478', username: 'user1', credential: 'pass2' }, // Different credential
        { urls: 'turn:test.com:3478', username: 'user2', credential: 'pass1' }, // Different username
      ];

      const deduplicatedServers = (
        connectionManager as any
      ).deduplicateIceServers(duplicateServers);

      expect(deduplicatedServers).toHaveLength(4); // Should remove only the exact duplicate
      expect(
        deduplicatedServers.filter(
          s => s.urls === 'stun:stun.l.google.com:19302'
        )
      ).toHaveLength(1);
    });
  });

  describe('Connection Management with TURN', () => {
    it('should handle connection creation with TURN servers', async () => {
      const turnProvider: TurnServerProvider = {
        name: 'test-turn',
        servers: [
          {
            urls: 'turn:test.example.com:3478',
            username: 'testuser',
            credential: 'testpass',
          },
        ],
      };

      connectionManager.addTurnProvider(turnProvider);

      const eventSpy = vi.fn();
      connectionManager.addEventListener('connection-created', eventSpy);

      const connection = await connectionManager.createConnection(
        'peer-with-turn',
        true
      );

      expect(connection).toBeDefined();
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            peerId: 'peer-with-turn',
            isInitiator: true,
          },
        })
      );
    });

    it('should handle connection failure gracefully', async () => {
      // Mock RTCPeerConnection constructor to throw
      global.RTCPeerConnection = vi.fn(() => {
        throw new Error('WebRTC not supported');
      }) as any;

      const eventSpy = vi.fn();
      connectionManager.addEventListener('connection-error', eventSpy);

      await expect(
        connectionManager.createConnection('failing-peer', true)
      ).rejects.toThrow('WebRTC not supported');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            peerId: 'failing-peer',
            error: expect.any(Error),
          },
        })
      );

      // Restore mock
      global.RTCPeerConnection = vi.fn(() => mockPeerConnection) as any;
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle network test errors gracefully', async () => {
      const eventSpy = vi.fn();
      connectionManager.addEventListener('network-test-error', eventSpy);

      // Mock the TURN manager to throw error
      vi.spyOn(connectionManager as any, 'turnManager').mockReturnValue({
        testNetworkConnectivity: vi
          .fn()
          .mockRejectedValue(new Error('Network test failed')),
      });

      await connectionManager.testNetworkConnectivity();

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { error: expect.any(Error) },
        })
      );
    });

    it('should cleanup TURN manager on destroy', () => {
      const destroySpy = vi.fn();
      vi.spyOn(connectionManager as any, 'turnManager').mockReturnValue({
        destroy: destroySpy,
      });

      connectionManager.cleanup();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Configuration Options', () => {
    it('should respect enableTurnFallback setting', () => {
      const managerWithoutFallback = new P2PConnectionManager({
        enableTurnFallback: false,
      });

      expect(managerWithoutFallback).toBeDefined();
      managerWithoutFallback.cleanup();
    });

    it('should use custom network test interval', () => {
      const customInterval = 600000; // 10 minutes
      const managerWithCustomInterval = new P2PConnectionManager({
        networkTestInterval: customInterval,
      });

      expect(managerWithCustomInterval).toBeDefined();
      managerWithCustomInterval.cleanup();
    });

    it('should handle empty ICE servers configuration', () => {
      const managerWithEmptyIce = new P2PConnectionManager({
        iceServers: [],
      });

      expect(managerWithEmptyIce).toBeDefined();
      managerWithEmptyIce.cleanup();
    });
  });
});
