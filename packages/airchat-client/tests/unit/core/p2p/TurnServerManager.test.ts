import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  TurnServerManager,
  type TurnServerProvider,
} from '../../../../src/core/p2p/TurnServerManager';

// Mock WebRTC APIs
const mockRTCPeerConnection = vi.fn(() => ({
  createDataChannel: vi.fn(),
  createOffer: vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
  setLocalDescription: vi.fn().mockResolvedValue(undefined),
  close: vi.fn(),
  onicecandidate: null,
}));

const mockRTCIceCandidate = vi.fn(init => ({
  type: init.type || 'host',
  port: init.port || 9,
  ...init,
}));

// Mock fetch
global.fetch = vi.fn();
global.RTCPeerConnection = mockRTCPeerConnection as any;
global.RTCIceCandidate = mockRTCIceCandidate as any;

describe('TurnServerManager', () => {
  let turnManager: TurnServerManager;

  beforeEach(() => {
    turnManager = new TurnServerManager();
    vi.clearAllMocks();
  });

  afterEach(() => {
    turnManager.destroy();
  });

  describe('Provider Management', () => {
    it('should initialize with default providers', () => {
      const status = turnManager.getConnectivityStatus();
      expect(status).toBeNull(); // No connectivity test run yet
    });

    it('should add custom TURN provider', () => {
      const customProvider: TurnServerProvider = {
        name: 'test-provider',
        servers: [
          {
            urls: 'turn:test.example.com:3478',
            username: 'testuser',
            credential: 'testpass',
            priority: 1,
          },
        ],
      };

      const eventSpy = vi.fn();
      turnManager.addEventListener('provider-added', eventSpy);

      turnManager.addTurnProvider(customProvider);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { provider: 'test-provider' },
        })
      );
    });

    it('should remove TURN provider', () => {
      const customProvider: TurnServerProvider = {
        name: 'test-provider',
        servers: [{ urls: 'turn:test.example.com:3478' }],
      };

      turnManager.addTurnProvider(customProvider);

      const eventSpy = vi.fn();
      turnManager.addEventListener('provider-removed', eventSpy);

      turnManager.removeTurnProvider('test-provider');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { provider: 'test-provider' },
        })
      );
    });
  });

  describe('Network Connectivity Testing', () => {
    it('should test network connectivity', async () => {
      const eventSpy = vi.fn();
      turnManager.addEventListener('connectivity-tested', eventSpy);

      // Mock successful STUN test
      const mockPeerConnection = {
        createDataChannel: vi.fn(),
        createOffer: vi
          .fn()
          .mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
        setLocalDescription: vi.fn().mockResolvedValue(undefined),
        close: vi.fn(),
        onicecandidate: null,
      };

      mockRTCPeerConnection.mockReturnValue(mockPeerConnection);

      // Start the test
      const testPromise = turnManager.testNetworkConnectivity();

      // Immediately simulate ICE candidate gathering
      const mockCandidate = {
        type: 'srflx',
        port: 54400,
        address: '*************',
      };

      if (mockPeerConnection.onicecandidate) {
        mockPeerConnection.onicecandidate({ candidate: mockCandidate });
        // Signal end of gathering
        mockPeerConnection.onicecandidate({ candidate: null });
      }

      const result = await testPromise;

      expect(result).toMatchObject({
        canConnectDirectly: expect.any(Boolean),
        natType: expect.any(String),
        requiresTurn: expect.any(Boolean),
        stunWorking: expect.any(Boolean),
        firewallRestrictions: expect.any(Array),
        recommendedServers: expect.any(Array),
      });

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { result },
        })
      );
    }, 10000);

    it('should detect symmetric NAT', async () => {
      const mockPeerConnection = {
        createDataChannel: vi.fn(),
        createOffer: vi
          .fn()
          .mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
        setLocalDescription: vi.fn().mockResolvedValue(undefined),
        close: vi.fn(),
        onicecandidate: null,
      };

      mockRTCPeerConnection.mockReturnValue(mockPeerConnection);

      // Start the test
      const testPromise = turnManager.testNetworkConnectivity();

      // Immediately simulate symmetric NAT (multiple srflx candidates with different ports)
      const candidates = [
        { type: 'srflx', port: 54400 },
        { type: 'srflx', port: 54401 },
      ];

      if (mockPeerConnection.onicecandidate) {
        candidates.forEach(candidate => {
          mockPeerConnection.onicecandidate({ candidate });
        });
        mockPeerConnection.onicecandidate({ candidate: null });
      }

      const result = await testPromise;

      expect(result.natType).toBe('symmetric');
      expect(result.requiresTurn).toBe(true);
    }, 10000);

    it('should handle connectivity test timeout', async () => {
      const mockPeerConnection = {
        createDataChannel: vi.fn(),
        createOffer: vi
          .fn()
          .mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
        setLocalDescription: vi.fn().mockResolvedValue(undefined),
        close: vi.fn(),
        onicecandidate: null,
      };

      mockRTCPeerConnection.mockReturnValue(mockPeerConnection);

      // Don't trigger any ICE candidates to simulate timeout
      const result = await turnManager.testNetworkConnectivity();

      expect(result.stunWorking).toBe(false);
      expect(result.requiresTurn).toBe(true);
    }, 15000);
  });

  describe('ICE Server Configuration', () => {
    it('should return STUN servers when TURN not required', async () => {
      // Mock connectivity test to not require TURN
      vi.spyOn(turnManager as any, 'testNetworkConnectivity').mockResolvedValue(
        {
          canConnectDirectly: true,
          natType: 'full-cone',
          requiresTurn: false,
          stunWorking: true,
          firewallRestrictions: [],
          recommendedServers: [],
        }
      );

      const iceServers = await turnManager.getIceServers();

      expect(iceServers).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ urls: expect.stringMatching(/^stun:/) }),
        ])
      );

      // Should not contain TURN servers
      const turnServers = iceServers.filter(server =>
        (Array.isArray(server.urls) ? server.urls[0] : server.urls).startsWith(
          'turn:'
        )
      );
      expect(turnServers).toHaveLength(0);
    });

    it('should include TURN servers when required', async () => {
      // Clear default providers first
      (turnManager as any).turnProviders.clear();

      // Add a provider with credentials
      const providerWithCredentials: TurnServerProvider = {
        name: 'test-turn',
        servers: [
          {
            urls: 'turn:test.example.com:3478',
            username: 'testuser',
            credential: 'testpass',
          },
        ],
      };

      turnManager.addTurnProvider(providerWithCredentials);

      // Mock the connectivity cache to require TURN
      (turnManager as any).connectivityCache = {
        canConnectDirectly: false,
        natType: 'symmetric',
        requiresTurn: true,
        stunWorking: false,
        firewallRestrictions: ['UDP traffic blocked'],
        recommendedServers: [],
        timestamp: Date.now(),
      };

      const iceServers = await turnManager.getIceServers();

      // Should contain both STUN and TURN servers
      const stunServers = iceServers.filter(server =>
        (Array.isArray(server.urls) ? server.urls[0] : server.urls).startsWith(
          'stun:'
        )
      );
      const turnServers = iceServers.filter(server =>
        (Array.isArray(server.urls) ? server.urls[0] : server.urls).startsWith(
          'turn:'
        )
      );

      expect(stunServers.length).toBeGreaterThan(0);
      expect(turnServers.length).toBeGreaterThan(0);
      expect(turnServers[0]).toMatchObject({
        urls: 'turn:test.example.com:3478',
        username: 'testuser',
        credential: 'testpass',
      });
    });
  });

  describe('Credential Management', () => {
    it('should refresh credentials from endpoint', async () => {
      // Clear default providers first
      (turnManager as any).turnProviders.clear();

      const mockCredentials = {
        username: 'dynamic-user',
        credential: 'dynamic-pass',
        ttl: 3600,
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCredentials),
      });

      const provider: TurnServerProvider = {
        name: 'dynamic-provider',
        servers: [{ urls: 'turn:dynamic.example.com:3478' }],
        credentialEndpoint: 'https://api.example.com/turn-credentials',
        apiKey: 'test-api-key',
      };

      turnManager.addTurnProvider(provider);

      // Mock the connectivity cache to require TURN
      (turnManager as any).connectivityCache = {
        requiresTurn: true,
        timestamp: Date.now(),
      };

      const iceServers = await turnManager.getIceServers();

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.example.com/turn-credentials',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            Authorization: 'Bearer test-api-key',
            'Content-Type': 'application/json',
          }),
        })
      );

      const turnServer = iceServers.find(server =>
        (Array.isArray(server.urls) ? server.urls[0] : server.urls).startsWith(
          'turn:'
        )
      );

      expect(turnServer).toMatchObject({
        username: 'dynamic-user',
        credential: 'dynamic-pass',
      });
    });

    it('should handle credential refresh failure gracefully', async () => {
      (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

      const provider: TurnServerProvider = {
        name: 'failing-provider',
        servers: [{ urls: 'turn:failing.example.com:3478' }],
        credentialEndpoint: 'https://api.example.com/turn-credentials',
        apiKey: 'test-api-key',
      };

      turnManager.addTurnProvider(provider);

      // Mock connectivity test to require TURN
      vi.spyOn(turnManager as any, 'testNetworkConnectivity').mockResolvedValue(
        {
          requiresTurn: true,
        }
      );

      // Should not throw error
      const iceServers = await turnManager.getIceServers();

      // Should still return STUN servers
      expect(iceServers.length).toBeGreaterThan(0);
      const stunServers = iceServers.filter(server =>
        (Array.isArray(server.urls) ? server.urls[0] : server.urls).startsWith(
          'stun:'
        )
      );
      expect(stunServers.length).toBeGreaterThan(0);
    });
  });

  describe('Cleanup', () => {
    it('should clean up resources on destroy', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

      turnManager.destroy();

      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(turnManager.getConnectivityStatus()).toBeNull();
    });
  });
});
