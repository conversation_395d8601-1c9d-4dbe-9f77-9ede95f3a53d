import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NetworkAnalyzer } from '../../../../src/core/p2p/NetworkAnalyzer';

// Mock WebRTC APIs
const mockDataChannel = {
  close: vi.fn(),
};

const mockPeerConnection = {
  createDataChannel: vi.fn(() => mockDataChannel),
  createOffer: vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
  setLocalDescription: vi.fn().mockResolvedValue(undefined),
  close: vi.fn(),
  onicecandidate: null,
};

global.RTCPeerConnection = vi.fn(() => mockPeerConnection) as any;

// Mock WebSocket
global.WebSocket = vi.fn(() => ({
  close: vi.fn(),
  onopen: null,
  onerror: null,
})) as any;

// Mock fetch
global.fetch = vi.fn();

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    mediaDevices: {
      getUserMedia: vi.fn(),
    },
  },
  writable: true,
});

describe('NetworkAnalyzer', () => {
  let analyzer: NetworkAnalyzer;

  beforeEach(() => {
    analyzer = new NetworkAnalyzer();
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any resources
  });

  describe('Capability Analysis', () => {
    it('should analyze browser capabilities', async () => {
      const capabilities = await analyzer.analyzeCapabilities();

      expect(capabilities).toMatchObject({
        supportsWebRTC: expect.any(Boolean),
        supportsDataChannels: expect.any(Boolean),
        supportsGetUserMedia: expect.any(Boolean),
        supportsWebSockets: expect.any(Boolean),
        browserInfo: {
          name: expect.any(String),
          version: expect.any(String),
          engine: expect.any(String),
        },
      });
    });

    it('should detect WebRTC support', async () => {
      const capabilities = await analyzer.analyzeCapabilities();

      expect(capabilities.supportsWebRTC).toBe(true);
      expect(capabilities.supportsDataChannels).toBe(true);
      expect(global.RTCPeerConnection).toHaveBeenCalled();
    });

    it('should detect browser information', async () => {
      const capabilities = await analyzer.analyzeCapabilities();

      expect(capabilities.browserInfo.name).toBe('Chrome');
      expect(capabilities.browserInfo.engine).toBe('Blink');
      expect(capabilities.browserInfo.version).toBe('120');
    });

    it('should handle WebRTC not supported', async () => {
      global.RTCPeerConnection = vi.fn(() => {
        throw new Error('WebRTC not supported');
      }) as any;

      const capabilities = await analyzer.analyzeCapabilities();

      expect(capabilities.supportsWebRTC).toBe(false);
      expect(capabilities.supportsDataChannels).toBe(false);

      // Restore mock
      global.RTCPeerConnection = vi.fn(() => mockPeerConnection) as any;
    });
  });

  describe('NAT Analysis', () => {
    it('should analyze NAT type', async () => {
      // Mock successful STUN response
      setTimeout(() => {
        if (mockPeerConnection.onicecandidate) {
          mockPeerConnection.onicecandidate({
            candidate: {
              type: 'srflx',
              port: 54400,
              candidate: 'candidate:1 1 UDP 2113667326 ************* 54400 typ srflx raddr ************* rport 54400',
            },
          });
          mockPeerConnection.onicecandidate({ candidate: null });
        }
      }, 100);

      const natAnalysis = await analyzer.analyzeNAT();

      expect(natAnalysis).toMatchObject({
        type: expect.any(String),
        confidence: expect.any(Number),
        mappedPorts: expect.any(Array),
        consistentMapping: expect.any(Boolean),
        supportsHairpinning: expect.any(Boolean),
      });

      expect(natAnalysis.confidence).toBeGreaterThan(0);
    }, 15000);

    it('should detect symmetric NAT', async () => {
      // Mock multiple STUN responses with different ports
      setTimeout(() => {
        if (mockPeerConnection.onicecandidate) {
          mockPeerConnection.onicecandidate({
            candidate: {
              type: 'srflx',
              port: 54400,
              candidate: 'candidate:1 1 UDP 2113667326 ************* 54400 typ srflx',
            },
          });
          mockPeerConnection.onicecandidate({
            candidate: {
              type: 'srflx',
              port: 54401,
              candidate: 'candidate:2 1 UDP 2113667326 ************* 54401 typ srflx',
            },
          });
          mockPeerConnection.onicecandidate({ candidate: null });
        }
      }, 100);

      const natAnalysis = await analyzer.analyzeNAT();

      expect(natAnalysis.consistentMapping).toBe(false);
      expect(natAnalysis.mappedPorts.length).toBeGreaterThan(1);
    }, 15000);

    it('should handle STUN server failures', async () => {
      // Don't trigger any ICE candidates to simulate failure
      const natAnalysis = await analyzer.analyzeNAT();

      expect(natAnalysis.type).toBe('unknown');
      expect(natAnalysis.confidence).toBe(0);
    }, 15000);
  });

  describe('Firewall Analysis', () => {
    it('should analyze firewall restrictions', async () => {
      // Mock WebSocket connections
      const mockWebSocket = {
        close: vi.fn(),
        onopen: null,
        onerror: null,
      };

      global.WebSocket = vi.fn(() => mockWebSocket) as any;

      // Simulate successful connection
      setTimeout(() => {
        if (mockWebSocket.onopen) {
          mockWebSocket.onopen({} as Event);
        }
      }, 100);

      const firewallAnalysis = await analyzer.analyzeFirewall();

      expect(firewallAnalysis).toMatchObject({
        blocksUDP: expect.any(Boolean),
        blocksTCP: expect.any(Boolean),
        blocksWebSockets: expect.any(Boolean),
        allowedPorts: expect.any(Array),
        blockedPorts: expect.any(Array),
        hasDeepPacketInspection: expect.any(Boolean),
        corporateFirewall: expect.any(Boolean),
      });
    });

    it('should detect corporate firewall', async () => {
      const mockWebSocket = {
        close: vi.fn(),
        onopen: null,
        onerror: null,
      };

      global.WebSocket = vi.fn(() => mockWebSocket) as any;

      // Simulate mixed success/failure pattern typical of corporate firewalls
      let callCount = 0;
      global.WebSocket = vi.fn(() => {
        const ws = {
          close: vi.fn(),
          onopen: null,
          onerror: null,
        };

        setTimeout(() => {
          if (callCount < 2) {
            // Allow standard ports (80, 443)
            if (ws.onopen) ws.onopen({} as Event);
          } else {
            // Block non-standard ports
            if (ws.onerror) ws.onerror({} as Event);
          }
          callCount++;
        }, 100);

        return ws;
      }) as any;

      const firewallAnalysis = await analyzer.analyzeFirewall();

      expect(firewallAnalysis.corporateFirewall).toBe(true);
      expect(firewallAnalysis.allowedPorts.length).toBeLessThan(firewallAnalysis.blockedPorts.length);
    });
  });

  describe('Bandwidth Measurement', () => {
    it('should measure network bandwidth', async () => {
      // Mock successful fetch responses
      (global.fetch as any).mockResolvedValue({
        ok: true,
      });

      const bandwidth = await analyzer.measureBandwidth();

      expect(bandwidth).toMatchObject({
        downloadSpeed: expect.any(Number),
        uploadSpeed: expect.any(Number),
        latency: expect.any(Number),
        jitter: expect.any(Number),
        packetLoss: expect.any(Number),
        timestamp: expect.any(Number),
      });

      expect(bandwidth.downloadSpeed).toBeGreaterThan(0);
      expect(bandwidth.uploadSpeed).toBeGreaterThan(0);
    });

    it('should handle fetch failures gracefully', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const bandwidth = await analyzer.measureBandwidth();

      expect(bandwidth.latency).toBe(0);
      expect(bandwidth.downloadSpeed).toBeGreaterThan(0); // Should still provide estimates
    });
  });

  describe('Comprehensive Analysis', () => {
    it('should perform complete network analysis', async () => {
      // Mock all the necessary responses
      (global.fetch as any).mockResolvedValue({ ok: true });

      const mockWebSocket = {
        close: vi.fn(),
        onopen: null,
        onerror: null,
      };

      global.WebSocket = vi.fn(() => mockWebSocket) as any;

      setTimeout(() => {
        if (mockWebSocket.onopen) {
          mockWebSocket.onopen({} as Event);
        }
      }, 100);

      setTimeout(() => {
        if (mockPeerConnection.onicecandidate) {
          mockPeerConnection.onicecandidate({
            candidate: {
              type: 'srflx',
              port: 54400,
              candidate: 'candidate:1 1 UDP 2113667326 ************* 54400 typ srflx',
            },
          });
          mockPeerConnection.onicecandidate({ candidate: null });
        }
      }, 200);

      const eventSpy = vi.fn();
      analyzer.addEventListener('analysis-complete', eventSpy);

      const analysis = await analyzer.analyzeNetwork();

      expect(analysis).toMatchObject({
        capabilities: expect.any(Object),
        nat: expect.any(Object),
        firewall: expect.any(Object),
        bandwidth: expect.any(Object),
        quality: expect.any(Object),
      });

      expect(analysis.quality).toMatchObject({
        overall: expect.stringMatching(/^(excellent|good|fair|poor)$/),
        p2pSuitability: expect.stringMatching(/^(ideal|suitable|challenging|unsuitable)$/),
        recommendedStrategy: expect.stringMatching(/^(direct|stun|turn|relay)$/),
        issues: expect.any(Array),
        recommendations: expect.any(Array),
      });

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: analysis,
        })
      );
    }, 20000);

    it('should assess network quality correctly', async () => {
      // Mock excellent network conditions
      (global.fetch as any).mockImplementation(() =>
        Promise.resolve({ ok: true })
      );

      const mockWebSocket = {
        close: vi.fn(),
        onopen: null,
        onerror: null,
      };

      global.WebSocket = vi.fn(() => mockWebSocket) as any;

      setTimeout(() => {
        if (mockWebSocket.onopen) {
          mockWebSocket.onopen({} as Event);
        }
      }, 50);

      setTimeout(() => {
        if (mockPeerConnection.onicecandidate) {
          // Simulate no NAT (direct connection possible)
          mockPeerConnection.onicecandidate({
            candidate: {
              type: 'host',
              port: 54400,
              candidate: 'candidate:1 1 UDP 2113667326 ************* 54400 typ host',
            },
          });
          mockPeerConnection.onicecandidate({ candidate: null });
        }
      }, 100);

      const analysis = await analyzer.analyzeNetwork();

      // With good conditions, should recommend direct or STUN
      expect(['direct', 'stun']).toContain(analysis.quality.recommendedStrategy);
      expect(['excellent', 'good']).toContain(analysis.quality.overall);
    }, 15000);
  });

  describe('Analysis Results', () => {
    it('should store and retrieve analysis results', () => {
      const results = analyzer.getAnalysisResults();

      expect(results).toMatchObject({
        capabilities: null, // Initially null
        nat: null,
        firewall: null,
        bandwidthHistory: expect.any(Array),
      });

      expect(results.bandwidthHistory).toHaveLength(0);
    });
  });
});
