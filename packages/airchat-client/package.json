{"name": "@airchat/client", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest run --ui", "test:e2e": "playwright test", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"@airchat/shared": "workspace:*", "date-fns": "^4.1.0", "dexie": "^4.0.11", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hotkeys-hook": "^5.1.0", "react-intersection-observer": "^9.16.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "workbox-window": "^7.3.0", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/file-saver": "^2.0.7", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/ui": "^3.2.4", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite": "^7.0.5", "vite-plugin-pwa": "^1.0.1", "vitest": "^3.2.4"}}