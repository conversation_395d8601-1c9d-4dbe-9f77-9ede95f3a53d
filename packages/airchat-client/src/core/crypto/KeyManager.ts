import { DeviceIdentity } from './E2EEncryption';

export interface StoredKeyPair {
  deviceId: string;
  publicKey: Uint8Array;
  privateKey: JsonWebKey;
  createdAt: number;
  lastUsed: number;
}

export interface ContactKey {
  deviceId: string;
  publicKey: Uint8Array;
  fingerprint: string;
  verified: boolean;
  addedAt: number;
  lastSeen: number;
}

export interface KeyBackup {
  version: number;
  deviceId: string;
  encryptedPrivateKey: string;
  salt: Uint8Array;
  iv: Uint8Array;
  iterations: number;
  createdAt: number;
}

export class KeyManager extends EventTarget {
  private readonly STORAGE_KEY_DEVICE = 'airchat_device_identity';
  private readonly STORAGE_KEY_CONTACTS = 'airchat_contact_keys';
  private readonly STORAGE_KEY_BACKUP = 'airchat_key_backup';
  
  private deviceIdentity: DeviceIdentity | null = null;
  private contactKeys: Map<string, ContactKey> = new Map();

  constructor() {
    super();
  }

  /**
   * Initialize key manager and load existing keys
   */
  async initialize(): Promise<void> {
    await this.loadDeviceIdentity();
    await this.loadContactKeys();
  }

  /**
   * Store device identity securely
   */
  async storeDeviceIdentity(identity: DeviceIdentity): Promise<void> {
    if (!identity.privateKey) {
      throw new Error('Private key is required for storage');
    }

    try {
      // Export private key for storage
      const privateKeyJwk = await crypto.subtle.exportKey('jwk', identity.privateKey);
      
      const storedKeyPair: StoredKeyPair = {
        deviceId: identity.deviceId,
        publicKey: identity.publicKey,
        privateKey: privateKeyJwk,
        createdAt: identity.createdAt,
        lastUsed: Date.now()
      };

      // Store in localStorage (in production, consider more secure storage)
      localStorage.setItem(this.STORAGE_KEY_DEVICE, JSON.stringify(storedKeyPair, this.replacer));
      
      this.deviceIdentity = identity;
      
      this.dispatchEvent(new CustomEvent('device-identity-stored', {
        detail: { deviceId: identity.deviceId }
      }));

    } catch (error) {
      console.error('Failed to store device identity:', error);
      throw new Error('Device identity storage failed');
    }
  }

  /**
   * Load device identity from storage
   */
  async loadDeviceIdentity(): Promise<DeviceIdentity | null> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY_DEVICE);
      if (!stored) {
        return null;
      }

      const storedKeyPair: StoredKeyPair = JSON.parse(stored, this.reviver);
      
      // Import private key
      const privateKey = await crypto.subtle.importKey(
        'jwk',
        storedKeyPair.privateKey,
        { name: 'ECDH', namedCurve: 'P-256' },
        true,
        ['deriveKey']
      );

      this.deviceIdentity = {
        deviceId: storedKeyPair.deviceId,
        publicKey: storedKeyPair.publicKey,
        privateKey,
        createdAt: storedKeyPair.createdAt
      };

      // Update last used timestamp
      storedKeyPair.lastUsed = Date.now();
      localStorage.setItem(this.STORAGE_KEY_DEVICE, JSON.stringify(storedKeyPair, this.replacer));

      return this.deviceIdentity;

    } catch (error) {
      console.error('Failed to load device identity:', error);
      // Clear corrupted data
      localStorage.removeItem(this.STORAGE_KEY_DEVICE);
      return null;
    }
  }

  /**
   * Get current device identity
   */
  getDeviceIdentity(): DeviceIdentity | null {
    return this.deviceIdentity;
  }

  /**
   * Add a contact's public key
   */
  async addContactKey(deviceId: string, publicKey: Uint8Array, verified: boolean = false): Promise<void> {
    try {
      // Generate fingerprint for verification
      const fingerprint = await this.generateFingerprint(publicKey);
      
      const contactKey: ContactKey = {
        deviceId,
        publicKey,
        fingerprint,
        verified,
        addedAt: Date.now(),
        lastSeen: Date.now()
      };

      this.contactKeys.set(deviceId, contactKey);
      await this.saveContactKeys();

      this.dispatchEvent(new CustomEvent('contact-key-added', {
        detail: { deviceId, fingerprint, verified }
      }));

    } catch (error) {
      console.error('Failed to add contact key:', error);
      throw new Error('Contact key addition failed');
    }
  }

  /**
   * Get a contact's public key
   */
  getContactKey(deviceId: string): ContactKey | null {
    return this.contactKeys.get(deviceId) || null;
  }

  /**
   * Verify a contact's key
   */
  async verifyContactKey(deviceId: string): Promise<void> {
    const contactKey = this.contactKeys.get(deviceId);
    if (!contactKey) {
      throw new Error('Contact key not found');
    }

    contactKey.verified = true;
    contactKey.lastSeen = Date.now();
    
    await this.saveContactKeys();

    this.dispatchEvent(new CustomEvent('contact-key-verified', {
      detail: { deviceId, fingerprint: contactKey.fingerprint }
    }));
  }

  /**
   * Remove a contact's key
   */
  async removeContactKey(deviceId: string): Promise<void> {
    const removed = this.contactKeys.delete(deviceId);
    if (removed) {
      await this.saveContactKeys();
      
      this.dispatchEvent(new CustomEvent('contact-key-removed', {
        detail: { deviceId }
      }));
    }
  }

  /**
   * Get all contact keys
   */
  getAllContactKeys(): ContactKey[] {
    return Array.from(this.contactKeys.values());
  }

  /**
   * Create encrypted backup of device keys
   */
  async createKeyBackup(password: string): Promise<KeyBackup> {
    if (!this.deviceIdentity?.privateKey) {
      throw new Error('No device identity to backup');
    }

    try {
      // Derive key from password
      const salt = crypto.getRandomValues(new Uint8Array(16));
      const iterations = 100000;
      
      const passwordKey = await this.deriveKeyFromPassword(password, salt, iterations);
      
      // Export and encrypt private key
      const privateKeyJwk = await crypto.subtle.exportKey('jwk', this.deviceIdentity.privateKey);
      const privateKeyData = new TextEncoder().encode(JSON.stringify(privateKeyJwk));
      
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encryptedPrivateKey = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        passwordKey,
        privateKeyData
      );

      const backup: KeyBackup = {
        version: 1,
        deviceId: this.deviceIdentity.deviceId,
        encryptedPrivateKey: this.arrayBufferToBase64(encryptedPrivateKey),
        salt,
        iv,
        iterations,
        createdAt: Date.now()
      };

      // Store backup
      localStorage.setItem(this.STORAGE_KEY_BACKUP, JSON.stringify(backup, this.replacer));

      return backup;

    } catch (error) {
      console.error('Failed to create key backup:', error);
      throw new Error('Key backup creation failed');
    }
  }

  /**
   * Restore keys from encrypted backup
   */
  async restoreFromBackup(backup: KeyBackup, password: string): Promise<DeviceIdentity> {
    try {
      // Derive key from password
      const passwordKey = await this.deriveKeyFromPassword(password, backup.salt, backup.iterations);
      
      // Decrypt private key
      const encryptedData = this.base64ToArrayBuffer(backup.encryptedPrivateKey);
      const decryptedData = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: backup.iv },
        passwordKey,
        encryptedData
      );

      const privateKeyJwk = JSON.parse(new TextDecoder().decode(decryptedData));
      
      // Import private key
      const privateKey = await crypto.subtle.importKey(
        'jwk',
        privateKeyJwk,
        { name: 'ECDH', namedCurve: 'P-256' },
        true,
        ['deriveKey']
      );

      // Derive public key
      const keyPair = await crypto.subtle.generateKey(
        { name: 'ECDH', namedCurve: 'P-256' },
        true,
        ['deriveKey']
      );

      // For ECDH, we need to reconstruct the public key from the private key
      // This is a simplified approach - in practice, you'd store the public key separately
      const publicKeyBuffer = await crypto.subtle.exportKey('raw', keyPair.publicKey);

      const restoredIdentity: DeviceIdentity = {
        deviceId: backup.deviceId,
        publicKey: new Uint8Array(publicKeyBuffer),
        privateKey,
        createdAt: backup.createdAt
      };

      await this.storeDeviceIdentity(restoredIdentity);

      this.dispatchEvent(new CustomEvent('keys-restored', {
        detail: { deviceId: backup.deviceId }
      }));

      return restoredIdentity;

    } catch (error) {
      console.error('Failed to restore from backup:', error);
      throw new Error('Key restoration failed');
    }
  }

  /**
   * Generate fingerprint for public key verification
   */
  async generateFingerprint(publicKey: Uint8Array): Promise<string> {
    const hash = await crypto.subtle.digest('SHA-256', publicKey);
    const hashArray = new Uint8Array(hash);
    
    // Format as human-readable fingerprint
    const hex = Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    // Group into 4-character segments for readability
    return hex.match(/.{1,4}/g)?.join(' ') || hex;
  }

  /**
   * Save contact keys to storage
   */
  private async saveContactKeys(): Promise<void> {
    try {
      const contactsArray = Array.from(this.contactKeys.values());
      localStorage.setItem(this.STORAGE_KEY_CONTACTS, JSON.stringify(contactsArray, this.replacer));
    } catch (error) {
      console.error('Failed to save contact keys:', error);
    }
  }

  /**
   * Load contact keys from storage
   */
  private async loadContactKeys(): Promise<void> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY_CONTACTS);
      if (!stored) {
        return;
      }

      const contactsArray: ContactKey[] = JSON.parse(stored, this.reviver);
      this.contactKeys.clear();
      
      for (const contact of contactsArray) {
        this.contactKeys.set(contact.deviceId, contact);
      }

    } catch (error) {
      console.error('Failed to load contact keys:', error);
      // Clear corrupted data
      localStorage.removeItem(this.STORAGE_KEY_CONTACTS);
    }
  }

  /**
   * Derive encryption key from password
   */
  private async deriveKeyFromPassword(
    password: string, 
    salt: Uint8Array, 
    iterations: number
  ): Promise<CryptoKey> {
    const passwordBuffer = new TextEncoder().encode(password);
    
    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations,
        hash: 'SHA-256'
      },
      baseKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Utility: Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Utility: Convert base64 to ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * JSON replacer for Uint8Array serialization
   */
  private replacer(key: string, value: any): any {
    if (value instanceof Uint8Array) {
      return { __type: 'Uint8Array', data: Array.from(value) };
    }
    return value;
  }

  /**
   * JSON reviver for Uint8Array deserialization
   */
  private reviver(key: string, value: any): any {
    if (value && value.__type === 'Uint8Array') {
      return new Uint8Array(value.data);
    }
    return value;
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.deviceIdentity = null;
    this.contactKeys.clear();
  }
}
