import { EncryptedMessage } from '@airchat/shared';

export interface DeviceIdentity {
  deviceId: string;
  publicKey: Uint8Array;
  privateKey?: CryptoKey; // Only stored locally
  createdAt: number;
}

export interface EncryptedFilePackage {
  id: string;
  fileName: string;
  fileSize: number;
  encryptedKey: Uint8Array;
  keyIv: Uint8Array;
  chunks: EncryptedFileChunk[];
  checksum: string;
  timestamp: number;
}

export interface EncryptedFileChunk {
  index: number;
  iv: Uint8Array;
  data: Uint8Array;
  checksum: string;
}

export interface KeyExchangeData {
  deviceId: string;
  publicKey: Uint8Array;
  signature: Uint8Array;
  timestamp: number;
}

export class E2EEncryption extends EventTarget {
  private keyPair: CryptoKeyPair | null = null;
  private deviceId: string | null = null;
  private sharedKeys: Map<string, CryptoKey> = new Map();
  private keyRotationInterval: number = 24 * 60 * 60 * 1000; // 24 hours
  private keyRotationTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    super();
  }

  /**
   * Initialize the encryption system with a device identity
   */
  async initialize(): Promise<DeviceIdentity> {
    if (this.keyPair && this.deviceId) {
      return this.getDeviceIdentity();
    }

    // Generate ECDH key pair for key exchange
    this.keyPair = await crypto.subtle.generateKey(
      {
        name: 'ECDH',
        namedCurve: 'P-256',
      },
      true, // extractable for backup/sync
      ['deriveKey']
    );

    // Generate unique device ID
    this.deviceId = await this.generateDeviceId();

    const identity = await this.getDeviceIdentity();

    this.dispatchEvent(
      new CustomEvent('identity-generated', {
        detail: { deviceId: this.deviceId },
      })
    );

    return identity;
  }

  /**
   * Get current device identity
   */
  async getDeviceIdentity(): Promise<DeviceIdentity> {
    if (!this.keyPair || !this.deviceId) {
      throw new Error('Encryption not initialized');
    }

    const publicKeyBuffer = await crypto.subtle.exportKey(
      'raw',
      this.keyPair.publicKey
    );

    return {
      deviceId: this.deviceId,
      publicKey: new Uint8Array(publicKeyBuffer),
      privateKey: this.keyPair.privateKey,
      createdAt: Date.now(),
    };
  }

  /**
   * Derive shared key with another device
   */
  async deriveSharedKey(
    contactDeviceId: string,
    contactPublicKey: Uint8Array
  ): Promise<void> {
    if (!this.keyPair) {
      throw new Error('Encryption not initialized');
    }

    try {
      // Import contact's public key
      const publicKey = await crypto.subtle.importKey(
        'raw',
        contactPublicKey,
        { name: 'ECDH', namedCurve: 'P-256' },
        false,
        []
      );

      // Derive shared key using ECDH
      const sharedKey = await crypto.subtle.deriveKey(
        { name: 'ECDH', public: publicKey },
        this.keyPair.privateKey,
        { name: 'AES-GCM', length: 256 },
        false,
        ['encrypt', 'decrypt']
      );

      this.sharedKeys.set(contactDeviceId, sharedKey);

      // Set up key rotation
      this.scheduleKeyRotation(contactDeviceId);

      this.dispatchEvent(
        new CustomEvent('shared-key-derived', {
          detail: { contactDeviceId },
        })
      );
    } catch (error) {
      console.error('Failed to derive shared key:', error);
      throw new Error('Key derivation failed');
    }
  }

  /**
   * Encrypt a text message
   */
  async encryptMessage(
    contactDeviceId: string,
    content: string
  ): Promise<EncryptedMessage> {
    const sharedKey = this.sharedKeys.get(contactDeviceId);
    if (!sharedKey) {
      throw new Error(`No shared key for contact ${contactDeviceId}`);
    }

    if (!this.deviceId) {
      throw new Error('Device not initialized');
    }

    try {
      // Generate random IV for this message
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const messageData = new TextEncoder().encode(content);

      // Encrypt the message
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        sharedKey,
        messageData
      );

      // Create key fingerprint for verification
      const keyFingerprint = await this.generateKeyFingerprint(sharedKey);

      return {
        id: crypto.randomUUID(),
        from: this.deviceId,
        to: contactDeviceId,
        encryptedContent: this.arrayBufferToBase64(encrypted),
        iv: this.arrayBufferToBase64(iv.buffer),
        timestamp: Date.now(),
        keyFingerprint,
      };
    } catch (error) {
      console.error('Failed to encrypt message:', error);
      throw new Error('Message encryption failed');
    }
  }

  /**
   * Decrypt a received message
   */
  async decryptMessage(encryptedMessage: EncryptedMessage): Promise<string> {
    const sharedKey = this.sharedKeys.get(encryptedMessage.from);
    if (!sharedKey) {
      throw new Error(`No shared key for sender ${encryptedMessage.from}`);
    }

    try {
      // Verify key fingerprint
      const expectedFingerprint = await this.generateKeyFingerprint(sharedKey);
      if (expectedFingerprint !== encryptedMessage.keyFingerprint) {
        throw new Error('Key fingerprint mismatch - possible tampering');
      }

      const iv = this.base64ToArrayBuffer(encryptedMessage.iv);
      const encryptedData = this.base64ToArrayBuffer(
        encryptedMessage.encryptedContent
      );

      // Decrypt the message
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        sharedKey,
        encryptedData
      );

      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      throw new Error('Message decryption failed');
    }
  }

  /**
   * Encrypt a file for transfer
   */
  async encryptFile(
    contactDeviceId: string,
    file: File
  ): Promise<EncryptedFilePackage> {
    const sharedKey = this.sharedKeys.get(contactDeviceId);
    if (!sharedKey) {
      throw new Error(`No shared key for contact ${contactDeviceId}`);
    }

    try {
      // Generate file-specific key
      const fileKey = await crypto.subtle.generateKey(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );

      // Encrypt the file key with shared key
      const fileKeyBuffer = await crypto.subtle.exportKey('raw', fileKey);
      const keyIv = crypto.getRandomValues(new Uint8Array(12));
      const encryptedFileKey = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv: keyIv },
        sharedKey,
        fileKeyBuffer
      );

      // Chunk and encrypt the file
      const chunks = await this.chunkFile(file);
      const encryptedChunks: EncryptedFileChunk[] = [];

      for (let i = 0; i < chunks.length; i++) {
        const chunkIv = crypto.getRandomValues(new Uint8Array(12));
        const encryptedChunk = await crypto.subtle.encrypt(
          { name: 'AES-GCM', iv: chunkIv },
          fileKey,
          chunks[i]
        );

        const checksum = await this.calculateChecksum(chunks[i]);

        encryptedChunks.push({
          index: i,
          iv: chunkIv,
          data: new Uint8Array(encryptedChunk),
          checksum,
        });
      }

      // Calculate overall file checksum
      const fileBuffer = await file.arrayBuffer();
      const fileChecksum = await this.calculateChecksum(
        new Uint8Array(fileBuffer)
      );

      return {
        id: crypto.randomUUID(),
        fileName: file.name,
        fileSize: file.size,
        encryptedKey: new Uint8Array(encryptedFileKey),
        keyIv,
        chunks: encryptedChunks,
        checksum: fileChecksum,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error('Failed to encrypt file:', error);
      throw new Error('File encryption failed');
    }
  }

  /**
   * Generate device ID from public key
   */
  private async generateDeviceId(): Promise<string> {
    if (!this.keyPair) {
      throw new Error('Key pair not generated');
    }

    const publicKeyBuffer = await crypto.subtle.exportKey(
      'raw',
      this.keyPair.publicKey
    );
    const hash = await crypto.subtle.digest('SHA-256', publicKeyBuffer);
    const hashArray = new Uint8Array(hash);

    // Convert to hex and take first 16 characters
    return Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
      .substring(0, 16);
  }

  /**
   * Generate key fingerprint for verification
   */
  private async generateKeyFingerprint(key: CryptoKey): Promise<string> {
    const keyBuffer = await crypto.subtle.exportKey('raw', key);
    const hash = await crypto.subtle.digest('SHA-256', keyBuffer);
    const hashArray = new Uint8Array(hash);

    return Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
      .substring(0, 8);
  }

  /**
   * Schedule key rotation for perfect forward secrecy
   */
  private scheduleKeyRotation(contactDeviceId: string): void {
    // Clear existing timer
    const existingTimer = this.keyRotationTimers.get(contactDeviceId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Schedule new key rotation
    const timer = setTimeout(() => {
      this.rotateKey(contactDeviceId);
    }, this.keyRotationInterval);

    this.keyRotationTimers.set(contactDeviceId, timer);
  }

  /**
   * Rotate shared key for perfect forward secrecy
   */
  private async rotateKey(contactDeviceId: string): Promise<void> {
    try {
      // Remove old key
      this.sharedKeys.delete(contactDeviceId);

      this.dispatchEvent(
        new CustomEvent('key-rotation-needed', {
          detail: { contactDeviceId },
        })
      );
    } catch (error) {
      console.error('Key rotation failed:', error);
    }
  }

  /**
   * Utility: Convert ArrayBuffer to base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Utility: Convert base64 to ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * Utility: Chunk file into manageable pieces
   */
  private async chunkFile(
    file: File,
    chunkSize: number = 4 * 1024 * 1024
  ): Promise<Uint8Array[]> {
    const chunks: Uint8Array[] = [];
    const fileBuffer = await file.arrayBuffer();
    const fileArray = new Uint8Array(fileBuffer);

    for (let i = 0; i < fileArray.length; i += chunkSize) {
      const chunk = fileArray.slice(i, i + chunkSize);
      chunks.push(chunk);
    }

    return chunks;
  }

  /**
   * Utility: Calculate SHA-256 checksum
   */
  private async calculateChecksum(data: Uint8Array): Promise<string> {
    const hash = await crypto.subtle.digest('SHA-256', data);
    const hashArray = new Uint8Array(hash);

    return Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clear all timers
    this.keyRotationTimers.forEach(timer => clearTimeout(timer));
    this.keyRotationTimers.clear();

    // Clear sensitive data
    this.sharedKeys.clear();
    this.keyPair = null;
    this.deviceId = null;
  }
}
