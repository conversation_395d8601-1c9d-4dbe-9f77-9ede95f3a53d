import { DeviceIdentity } from '../crypto/E2EEncryption';

export interface DiscoveryServerConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
}

export interface ServerDiscoveredContact {
  deviceId: string;
  deviceName: string;
  publicKey: string; // Base64 encoded
  lastSeen: number;
  location?: {
    country?: string;
    region?: string;
  };
}

export interface ServerContactRequest {
  id: string;
  fromDeviceId: string;
  toDeviceId: string;
  message?: string;
  timestamp: number;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  expiresAt: number;
}

export class DiscoveryClient {
  private config: DiscoveryServerConfig;
  private deviceIdentity: DeviceIdentity | null = null;

  constructor(config: Partial<DiscoveryServerConfig> = {}) {
    this.config = {
      baseUrl: 'https://airchat-signaling.your-domain.workers.dev',
      timeout: 10000,
      retryAttempts: 3,
      ...config,
    };
  }

  /**
   * Initialize with device identity
   */
  initialize(deviceIdentity: DeviceIdentity): void {
    this.deviceIdentity = deviceIdentity;
  }

  /**
   * Register device with the discovery server
   */
  async registerDevice(
    deviceName: string,
    discoverable: boolean = true
  ): Promise<void> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const publicKeyBase64 = this.arrayBufferToBase64(
      this.deviceIdentity.publicKey.buffer as ArrayBuffer
    );

    const response = await this.makeRequest('/api/register-device', {
      method: 'POST',
      body: JSON.stringify({
        deviceId: this.deviceIdentity.deviceId,
        publicKey: publicKeyBase64,
        deviceName,
        discoverable,
      }),
    });

    if (!response.success) {
      throw new Error(`Device registration failed: ${response.error}`);
    }
  }

  /**
   * Update device presence on the server
   */
  async updatePresence(isOnline: boolean = true): Promise<void> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const response = await this.makeRequest('/api/update-presence', {
      method: 'POST',
      body: JSON.stringify({
        deviceId: this.deviceIdentity.deviceId,
        isOnline,
      }),
    });

    if (!response.success) {
      throw new Error(`Presence update failed: ${response.error}`);
    }
  }

  /**
   * Discover contacts from the server
   */
  async discoverContacts(
    maxResults: number = 20,
    excludeDeviceIds: string[] = []
  ): Promise<ServerDiscoveredContact[]> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const response = await this.makeRequest('/api/discover-contacts', {
      method: 'POST',
      body: JSON.stringify({
        deviceId: this.deviceIdentity.deviceId,
        maxResults,
        excludeDeviceIds,
      }),
    });

    if (!response.contacts) {
      throw new Error(
        `Contact discovery failed: ${response.error || 'Unknown error'}`
      );
    }

    return response.contacts;
  }

  /**
   * Send a contact request through the server
   */
  async sendContactRequest(
    toDeviceId: string,
    message?: string
  ): Promise<string> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const response = await this.makeRequest('/api/contact-request', {
      method: 'POST',
      body: JSON.stringify({
        fromDeviceId: this.deviceIdentity.deviceId,
        toDeviceId,
        message,
      }),
    });

    if (!response.success) {
      throw new Error(`Contact request failed: ${response.error}`);
    }

    return response.requestId;
  }

  /**
   * Respond to a contact request
   */
  async respondToContactRequest(
    requestId: string,
    accepted: boolean
  ): Promise<void> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const response = await this.makeRequest('/api/contact-response', {
      method: 'POST',
      body: JSON.stringify({
        requestId,
        accepted,
        deviceId: this.deviceIdentity.deviceId,
      }),
    });

    if (!response.success) {
      throw new Error(`Contact response failed: ${response.error}`);
    }
  }

  /**
   * Make HTTP request to the discovery server
   */
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<any> {
    const url = `${this.config.baseUrl}${endpoint}`;

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      signal: AbortSignal.timeout(this.config.timeout),
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, requestOptions);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            `HTTP ${response.status}: ${errorData.error || response.statusText}`
          );
        }

        return await response.json();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Don't retry on client errors (4xx)
        if (error instanceof Error && error.message.includes('HTTP 4')) {
          throw error;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < this.config.retryAttempts - 1) {
          const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s...
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('Request failed after all retry attempts');
  }

  /**
   * Convert ArrayBuffer to base64 string
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Convert base64 string to Uint8Array
   */
  private base64ToUint8Array(base64: string): Uint8Array {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * Convert server contact to local format
   */
  convertServerContact(serverContact: ServerDiscoveredContact): {
    deviceId: string;
    publicKey: Uint8Array;
    deviceName: string;
    lastSeen: number;
    location?: { country?: string; region?: string };
  } {
    return {
      deviceId: serverContact.deviceId,
      publicKey: this.base64ToUint8Array(serverContact.publicKey),
      deviceName: serverContact.deviceName,
      lastSeen: serverContact.lastSeen,
      location: serverContact.location,
    };
  }

  /**
   * Test connection to the discovery server
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch (error) {
      console.error('Discovery server connection test failed:', error);
      return false;
    }
  }

  /**
   * Get server configuration
   */
  getConfig(): DiscoveryServerConfig {
    return { ...this.config };
  }

  /**
   * Update server configuration
   */
  updateConfig(newConfig: Partial<DiscoveryServerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
