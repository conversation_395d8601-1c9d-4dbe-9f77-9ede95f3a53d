import { DeviceIdentity } from '../crypto/E2EEncryption';
import { P2PConnectionManager } from '../p2p/P2PConnectionManager';
import { DiscoveryClient } from './DiscoveryClient';

export interface DiscoveredContact {
  deviceId: string;
  publicKey: Uint8Array;
  deviceName?: string;
  lastSeen: number;
  distance?: number; // Network distance/latency
  signalStrength?: number; // For future wireless implementations
  discoveryMethod: 'broadcast' | 'webrtc' | 'manual';
  verified: boolean;
}

export interface DiscoveryConfig {
  broadcastInterval: number; // How often to broadcast presence
  discoveryTimeout: number; // How long to wait for responses
  maxDiscoveredContacts: number; // Limit discovered contacts
  enableAutoBroadcast: boolean; // Auto-broadcast presence
}

export interface ContactRequest {
  id: string;
  from: string;
  to: string;
  fromDeviceId: string;
  fromPublicKey: Uint8Array;
  fromDeviceName?: string;
  message?: string;
  timestamp: number;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
}

export class ContactDiscovery extends EventTarget {
  private config: Required<DiscoveryConfig>;
  private connectionManager: P2PConnectionManager;
  private discoveryClient: DiscoveryClient;
  private deviceIdentity: DeviceIdentity | null = null;
  private discoveredContacts = new Map<string, DiscoveredContact>();
  private contactRequests = new Map<string, ContactRequest>();
  private isDiscovering = false;
  private presenceTimer: number | null = null;
  private discoveryTimer: number | null = null;

  constructor(
    connectionManager: P2PConnectionManager,
    config: Partial<DiscoveryConfig> = {}
  ) {
    super();

    this.connectionManager = connectionManager;
    this.discoveryClient = new DiscoveryClient();
    this.config = {
      broadcastInterval: 30000, // 30 seconds
      discoveryTimeout: 10000, // 10 seconds
      maxDiscoveredContacts: 50,
      enableAutoBroadcast: true,
      ...config,
    };

    this.setupEventHandlers();
  }

  /**
   * Initialize discovery with device identity
   */
  async initialize(deviceIdentity: DeviceIdentity): Promise<void> {
    this.deviceIdentity = deviceIdentity;
    this.discoveryClient.initialize(deviceIdentity);

    try {
      // Register device with the discovery server
      await this.discoveryClient.registerDevice('AirChat User', true);

      if (this.config.enableAutoBroadcast) {
        this.startPresenceUpdates();
      }

      this.dispatchEvent(
        new CustomEvent('discovery-initialized', {
          detail: { deviceId: deviceIdentity.deviceId },
        })
      );
    } catch (error) {
      console.error('Failed to initialize discovery:', error);
      throw error;
    }
  }

  /**
   * Start discovering contacts on the network
   */
  async startDiscovery(): Promise<void> {
    if (this.isDiscovering || !this.deviceIdentity) {
      return;
    }

    this.isDiscovering = true;
    this.discoveredContacts.clear();

    this.dispatchEvent(new CustomEvent('discovery-started'));

    try {
      // Get existing contact IDs to exclude from discovery
      const excludeDeviceIds = Array.from(this.discoveredContacts.keys());
      excludeDeviceIds.push(this.deviceIdentity.deviceId); // Exclude self

      // Discover contacts from server
      const serverContacts = await this.discoveryClient.discoverContacts(
        this.config.maxDiscoveredContacts,
        excludeDeviceIds
      );

      // Convert server contacts to local format
      for (const serverContact of serverContacts) {
        const localContact =
          this.discoveryClient.convertServerContact(serverContact);
        const discoveredContact: DiscoveredContact = {
          ...localContact,
          discoveryMethod: 'webrtc',
          verified: false,
        };

        this.discoveredContacts.set(
          discoveredContact.deviceId,
          discoveredContact
        );

        this.dispatchEvent(
          new CustomEvent('contact-discovered', {
            detail: { contact: discoveredContact },
          })
        );
      }

      // Set timeout for discovery
      this.discoveryTimer = window.setTimeout(() => {
        this.stopDiscovery();
      }, this.config.discoveryTimeout);
    } catch (error) {
      console.error('Failed to start discovery:', error);
      this.stopDiscovery();
      throw error;
    }
  }

  /**
   * Stop discovering contacts
   */
  stopDiscovery(): void {
    if (!this.isDiscovering) return;

    this.isDiscovering = false;

    if (this.discoveryTimer) {
      clearTimeout(this.discoveryTimer);
      this.discoveryTimer = null;
    }

    this.dispatchEvent(
      new CustomEvent('discovery-stopped', {
        detail: {
          contactsFound: this.discoveredContacts.size,
          contacts: Array.from(this.discoveredContacts.values()),
        },
      })
    );
  }

  /**
   * Send a contact request to a discovered contact
   */
  async sendContactRequest(
    targetDeviceId: string,
    message?: string
  ): Promise<string> {
    if (!this.deviceIdentity) {
      throw new Error('Device identity not initialized');
    }

    const contact = this.discoveredContacts.get(targetDeviceId);
    if (!contact) {
      throw new Error('Contact not found in discovered contacts');
    }

    const request: ContactRequest = {
      id: crypto.randomUUID(),
      from: this.deviceIdentity.deviceId,
      to: targetDeviceId,
      fromDeviceId: this.deviceIdentity.deviceId,
      fromPublicKey: this.deviceIdentity.publicKey,
      fromDeviceName: 'AirChat User', // TODO: Get from settings
      message,
      timestamp: Date.now(),
      status: 'pending',
    };

    try {
      // Send request via discovery server
      const requestId = await this.discoveryClient.sendContactRequest(
        targetDeviceId,
        message
      );

      // Update the request with the server-generated ID
      request.id = requestId;
      this.contactRequests.set(request.id, request);

      this.dispatchEvent(
        new CustomEvent('contact-request-sent', {
          detail: { request, targetDeviceId },
        })
      );

      return request.id;
    } catch (error) {
      console.error('Failed to send contact request:', error);
      throw error;
    }
  }

  /**
   * Respond to a contact request
   */
  async respondToContactRequest(
    requestId: string,
    accept: boolean
  ): Promise<void> {
    const request = this.contactRequests.get(requestId);
    if (!request) {
      throw new Error('Contact request not found');
    }

    request.status = accept ? 'accepted' : 'rejected';

    try {
      // Send response via discovery server
      await this.discoveryClient.respondToContactRequest(requestId, accept);

      this.dispatchEvent(
        new CustomEvent('contact-request-responded', {
          detail: { request, accepted: accept },
        })
      );

      if (accept) {
        // Add to discovered contacts if accepted
        const discoveredContact: DiscoveredContact = {
          deviceId: request.fromDeviceId,
          publicKey: request.fromPublicKey,
          deviceName: request.fromDeviceName,
          lastSeen: Date.now(),
          discoveryMethod: 'webrtc',
          verified: true, // Mark as verified since request was accepted
        };

        this.discoveredContacts.set(request.fromDeviceId, discoveredContact);

        this.dispatchEvent(
          new CustomEvent('contact-discovered', {
            detail: { contact: discoveredContact },
          })
        );
      }
    } catch (error) {
      console.error('Failed to respond to contact request:', error);
      throw error;
    }
  }

  /**
   * Get all discovered contacts
   */
  getDiscoveredContacts(): DiscoveredContact[] {
    return Array.from(this.discoveredContacts.values());
  }

  /**
   * Get pending contact requests
   */
  getPendingContactRequests(): ContactRequest[] {
    return Array.from(this.contactRequests.values()).filter(
      request => request.status === 'pending'
    );
  }

  /**
   * Clear discovered contacts
   */
  clearDiscoveredContacts(): void {
    this.discoveredContacts.clear();
    this.dispatchEvent(new CustomEvent('contacts-cleared'));
  }

  /**
   * Get all contact requests
   */
  getContactRequests(): Map<string, ContactRequest> {
    return this.contactRequests;
  }

  /**
   * Check if discovery is currently active
   */
  getIsDiscovering(): boolean {
    return this.isDiscovering;
  }

  /**
   * Start periodic presence updates to the server
   */
  private startPresenceUpdates(): void {
    if (this.presenceTimer) {
      clearInterval(this.presenceTimer);
    }

    this.presenceTimer = window.setInterval(async () => {
      try {
        await this.discoveryClient.updatePresence(true);
      } catch (error) {
        console.error('Failed to update presence:', error);
      }
    }, this.config.broadcastInterval);
  }

  /**
   * Broadcast message to all available data channels
   */
  private async broadcastToDataChannels(message: any): Promise<void> {
    // This is a simplified implementation
    // In a real implementation, we'd use the connection manager's data channels
    console.log('Broadcasting discovery message:', message);

    // For now, we'll simulate discovery by creating some mock contacts
    if (message.type === 'discovery-request') {
      setTimeout(() => {
        this.simulateDiscoveredContact();
      }, 1000);
    }
  }

  /**
   * Send discovery message to specific device
   */
  private async sendDiscoveryMessage(
    deviceId: string,
    message: any
  ): Promise<void> {
    console.log(`Sending discovery message to ${deviceId}:`, message);
    // TODO: Implement actual message sending via WebRTC
  }

  /**
   * Handle incoming discovery messages
   */
  private handleDiscoveryMessage(message: any, _fromDeviceId: string): void {
    switch (message.type) {
      case 'discovery-request':
        this.handleDiscoveryRequest(message, _fromDeviceId);
        break;
      case 'discovery-response':
        this.handleDiscoveryResponse(message, _fromDeviceId);
        break;
      case 'presence':
        this.handlePresence(message, _fromDeviceId);
        break;
      case 'contact-request':
        this.handleContactRequest(message, _fromDeviceId);
        break;
      case 'contact-response':
        this.handleContactResponse(message, _fromDeviceId);
        break;
    }
  }

  /**
   * Handle discovery request from another device
   */
  private handleDiscoveryRequest(message: any, fromDeviceId: string): void {
    if (!this.deviceIdentity || fromDeviceId === this.deviceIdentity.deviceId) {
      return;
    }

    // Add to discovered contacts
    const contact: DiscoveredContact = {
      deviceId: message.deviceId,
      publicKey: new Uint8Array(message.publicKey),
      deviceName: message.deviceName,
      lastSeen: Date.now(),
      discoveryMethod: 'webrtc',
      verified: false,
    };

    this.discoveredContacts.set(message.deviceId, contact);

    this.dispatchEvent(
      new CustomEvent('contact-discovered', {
        detail: { contact },
      })
    );

    // Send response
    this.sendDiscoveryMessage(fromDeviceId, {
      type: 'discovery-response',
      deviceId: this.deviceIdentity.deviceId,
      publicKey: Array.from(this.deviceIdentity.publicKey),
      deviceName: 'AirChat User',
      timestamp: Date.now(),
    });
  }

  /**
   * Handle discovery response from another device
   */
  private handleDiscoveryResponse(message: any, _fromDeviceId: string): void {
    const contact: DiscoveredContact = {
      deviceId: message.deviceId,
      publicKey: new Uint8Array(message.publicKey),
      deviceName: message.deviceName,
      lastSeen: Date.now(),
      discoveryMethod: 'webrtc',
      verified: false,
    };

    this.discoveredContacts.set(message.deviceId, contact);

    this.dispatchEvent(
      new CustomEvent('contact-discovered', {
        detail: { contact },
      })
    );
  }

  /**
   * Handle presence broadcast from another device
   */
  private handlePresence(message: any, _fromDeviceId: string): void {
    const existingContact = this.discoveredContacts.get(message.deviceId);
    if (existingContact) {
      existingContact.lastSeen = Date.now();
    }
  }

  /**
   * Handle incoming contact request
   */
  private handleContactRequest(message: any, _fromDeviceId: string): void {
    const request = message.request as ContactRequest;
    this.contactRequests.set(request.id, request);

    this.dispatchEvent(
      new CustomEvent('contact-request-received', {
        detail: { request },
      })
    );
  }

  /**
   * Handle contact request response
   */
  private handleContactResponse(message: any, _fromDeviceId: string): void {
    const request = this.contactRequests.get(message.requestId);
    if (request) {
      request.status = message.accepted ? 'accepted' : 'rejected';

      this.dispatchEvent(
        new CustomEvent('contact-request-response-received', {
          detail: { request, accepted: message.accepted },
        })
      );
    }
  }

  /**
   * Simulate discovering a contact (for testing)
   */
  private simulateDiscoveredContact(): void {
    // Create multiple mock contacts for testing
    const mockContacts = [
      {
        deviceId: `alice-device-${Date.now()}`,
        deviceName: "Alice's iPhone",
        publicKey: new Uint8Array(65).fill(42),
      },
      {
        deviceId: `bob-device-${Date.now() + 1}`,
        deviceName: "Bob's MacBook",
        publicKey: new Uint8Array(65).fill(123),
      },
      {
        deviceId: `charlie-device-${Date.now() + 2}`,
        deviceName: "Charlie's Android",
        publicKey: new Uint8Array(65).fill(200),
      },
    ];

    // Add contacts with a slight delay to simulate real discovery
    mockContacts.forEach((contactData, index) => {
      setTimeout(() => {
        const mockContact: DiscoveredContact = {
          deviceId: contactData.deviceId,
          publicKey: contactData.publicKey,
          deviceName: contactData.deviceName,
          lastSeen: Date.now(),
          discoveryMethod: 'webrtc',
          verified: false,
        };

        this.discoveredContacts.set(mockContact.deviceId, mockContact);

        this.dispatchEvent(
          new CustomEvent('contact-discovered', {
            detail: { contact: mockContact },
          })
        );
      }, index * 500); // 500ms delay between each contact
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Listen for connection manager events
    this.connectionManager.addEventListener(
      'message-received',
      (event: any) => {
        const { message } = event.detail;
        // Handle discovery-related messages here if needed
        console.log('Received message:', message);
      }
    );
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    this.stopDiscovery();
    this.stopPresenceUpdates();
    this.discoveredContacts.clear();
    this.contactRequests.clear();
  }

  /**
   * Stop presence updates
   */
  private stopPresenceUpdates(): void {
    if (this.presenceTimer) {
      clearInterval(this.presenceTimer);
      this.presenceTimer = null;
    }
  }
}
