/**
 * Authentication Service for P2P Messaging
 * 
 * Handles user registration, login, JWT token management, and session handling
 */

export interface UserProfile {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  createdAt: number;
  lastLoginAt: number;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    mobile: boolean;
  };
  privacy: {
    discoverable: boolean;
    showOnlineStatus: boolean;
    allowContactRequests: boolean;
  };
  security: {
    requireDeviceVerification: boolean;
    sessionTimeout: number;
  };
}

export interface DeviceInfo {
  id: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'web';
  isVerified: boolean;
  isTrusted: boolean;
  lastSeenAt: number;
}

export interface AuthSession {
  token: string;
  refreshToken: string;
  expiresAt: number;
}

export interface RegistrationData {
  username: string;
  email: string;
  password: string;
  displayName: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthenticationResult {
  success: boolean;
  user?: UserProfile;
  session?: AuthSession;
  device?: DeviceInfo;
  error?: string;
}

export class AuthenticationService extends EventTarget {
  private signalingUrl: string;
  private currentUser: UserProfile | null = null;
  private currentSession: AuthSession | null = null;
  private deviceId: string;
  private deviceInfo: DeviceInfo | null = null;
  private refreshTimer: number | null = null;

  constructor(signalingUrl: string) {
    super();
    this.signalingUrl = signalingUrl;
    this.deviceId = this.getOrCreateDeviceId();
    this.loadStoredSession();
  }

  /**
   * Register a new user account
   */
  async register(data: RegistrationData): Promise<AuthenticationResult> {
    try {
      const deviceInfo = await this.getDeviceInfo();
      
      const response = await fetch(`${this.signalingUrl}/api/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          deviceId: this.deviceId,
          deviceName: deviceInfo.name,
          deviceType: deviceInfo.type,
          publicKey: await this.getDevicePublicKey(),
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        return { success: false, error: result.error || 'Registration failed' };
      }

      // Store session and user data
      this.currentUser = result.user;
      this.currentSession = result.session;
      this.deviceInfo = result.device;

      this.storeSession();
      this.scheduleTokenRefresh();

      this.emit('user-registered', { user: this.currentUser });
      this.emit('authentication-changed', { authenticated: true, user: this.currentUser });

      return {
        success: true,
        user: this.currentUser,
        session: this.currentSession,
        device: this.deviceInfo,
      };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error during registration' };
    }
  }

  /**
   * Login with existing credentials
   */
  async login(credentials: LoginCredentials): Promise<AuthenticationResult> {
    try {
      const deviceInfo = await this.getDeviceInfo();
      
      const response = await fetch(`${this.signalingUrl}/api/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...credentials,
          deviceId: this.deviceId,
          deviceName: deviceInfo.name,
          deviceType: deviceInfo.type,
          publicKey: await this.getDevicePublicKey(),
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        return { success: false, error: result.error || 'Login failed' };
      }

      // Store session and user data
      this.currentUser = result.user;
      this.currentSession = result.session;
      this.deviceInfo = result.device;

      this.storeSession();
      this.scheduleTokenRefresh();

      this.emit('user-logged-in', { user: this.currentUser });
      this.emit('authentication-changed', { authenticated: true, user: this.currentUser });

      return {
        success: true,
        user: this.currentUser,
        session: this.currentSession,
        device: this.deviceInfo,
      };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error during login' };
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<void> {
    try {
      if (this.currentSession) {
        await fetch(`${this.signalingUrl}/api/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.currentSession.token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearSession();
      this.emit('user-logged-out');
      this.emit('authentication-changed', { authenticated: false, user: null });
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<UserProfile | null> {
    if (!this.currentSession) {
      return null;
    }

    try {
      const response = await fetch(`${this.signalingUrl}/api/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.currentSession.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          this.clearSession();
          this.emit('authentication-changed', { authenticated: false, user: null });
        }
        return null;
      }

      const result = await response.json();
      this.currentUser = result.user;
      this.deviceInfo = result.devices.find((d: DeviceInfo) => d.id === this.deviceId) || null;

      return this.currentUser;
    } catch (error) {
      console.error('Get profile error:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<Pick<UserProfile, 'displayName' | 'avatar' | 'preferences'>>): Promise<boolean> {
    if (!this.currentSession) {
      return false;
    }

    try {
      const response = await fetch(`${this.signalingUrl}/api/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.currentSession.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        if (response.status === 401) {
          this.clearSession();
          this.emit('authentication-changed', { authenticated: false, user: null });
        }
        return false;
      }

      const result = await response.json();
      this.currentUser = result.user;
      this.storeSession();

      this.emit('profile-updated', { user: this.currentUser });

      return true;
    } catch (error) {
      console.error('Update profile error:', error);
      return false;
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentSession !== null && this.currentUser !== null;
  }

  /**
   * Get current user
   */
  getCurrentUser(): UserProfile | null {
    return this.currentUser;
  }

  /**
   * Get current session
   */
  getCurrentSession(): AuthSession | null {
    return this.currentSession;
  }

  /**
   * Get device information
   */
  getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo;
  }

  /**
   * Get authentication token for API requests
   */
  getAuthToken(): string | null {
    return this.currentSession?.token || null;
  }

  /**
   * Get or create device ID
   */
  private getOrCreateDeviceId(): string {
    let deviceId = localStorage.getItem('airchat-device-id');
    if (!deviceId) {
      deviceId = crypto.randomUUID();
      localStorage.setItem('airchat-device-id', deviceId);
    }
    return deviceId;
  }

  /**
   * Get device information for registration/login
   */
  private async getDeviceInfo(): Promise<{ name: string; type: 'desktop' | 'mobile' | 'tablet' | 'web' }> {
    // Detect device type
    const userAgent = navigator.userAgent.toLowerCase();
    let type: 'desktop' | 'mobile' | 'tablet' | 'web' = 'web';
    
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/.test(userAgent)) {
      type = 'mobile';
    } else if (/tablet|ipad/.test(userAgent)) {
      type = 'tablet';
    } else if (/electron/.test(userAgent)) {
      type = 'desktop';
    }

    // Generate device name
    const platform = navigator.platform || 'Unknown';
    const browserName = this.getBrowserName();
    const name = `${browserName} on ${platform}`;

    return { name, type };
  }

  /**
   * Get browser name
   */
  private getBrowserName(): string {
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    
    return 'Unknown Browser';
  }

  /**
   * Get device public key (simplified for demo)
   */
  private async getDevicePublicKey(): Promise<string> {
    // In a real implementation, you'd generate or retrieve a proper public key
    // For demo purposes, we'll use a simple identifier
    return `pubkey_${this.deviceId}`;
  }

  /**
   * Store session in localStorage
   */
  private storeSession(): void {
    if (this.currentUser && this.currentSession) {
      localStorage.setItem('airchat-user', JSON.stringify(this.currentUser));
      localStorage.setItem('airchat-session', JSON.stringify(this.currentSession));
      if (this.deviceInfo) {
        localStorage.setItem('airchat-device', JSON.stringify(this.deviceInfo));
      }
    }
  }

  /**
   * Load stored session from localStorage
   */
  private loadStoredSession(): void {
    try {
      const userStr = localStorage.getItem('airchat-user');
      const sessionStr = localStorage.getItem('airchat-session');
      const deviceStr = localStorage.getItem('airchat-device');

      if (userStr && sessionStr) {
        this.currentUser = JSON.parse(userStr);
        this.currentSession = JSON.parse(sessionStr);
        
        if (deviceStr) {
          this.deviceInfo = JSON.parse(deviceStr);
        }

        // Check if session is still valid
        if (this.currentSession.expiresAt > Date.now()) {
          this.scheduleTokenRefresh();
          this.emit('authentication-changed', { authenticated: true, user: this.currentUser });
        } else {
          this.clearSession();
        }
      }
    } catch (error) {
      console.error('Failed to load stored session:', error);
      this.clearSession();
    }
  }

  /**
   * Clear session data
   */
  private clearSession(): void {
    this.currentUser = null;
    this.currentSession = null;
    this.deviceInfo = null;

    localStorage.removeItem('airchat-user');
    localStorage.removeItem('airchat-session');
    localStorage.removeItem('airchat-device');

    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Schedule token refresh
   */
  private scheduleTokenRefresh(): void {
    if (!this.currentSession) return;

    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Refresh token 5 minutes before expiration
    const refreshTime = this.currentSession.expiresAt - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = window.setTimeout(() => {
        this.refreshToken();
      }, refreshTime);
    }
  }

  /**
   * Refresh authentication token
   */
  private async refreshToken(): Promise<void> {
    if (!this.currentSession?.refreshToken) {
      this.clearSession();
      this.emit('authentication-changed', { authenticated: false, user: null });
      return;
    }

    try {
      // In a real implementation, you'd have a refresh token endpoint
      // For now, we'll just extend the current session
      this.currentSession.expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      this.storeSession();
      this.scheduleTokenRefresh();
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearSession();
      this.emit('authentication-changed', { authenticated: false, user: null });
    }
  }

  /**
   * Emit events
   */
  private emit(type: string, detail?: any): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
