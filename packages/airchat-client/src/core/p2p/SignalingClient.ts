import type {
  AnySignalingMessage,
  OfferMessage,
  AnswerMessage,
  IceCandidateMessage,
  JoinRoomMessage,
  LeaveRoomMessage,
  PresenceUpdateMessage,
  SignalingError
} from '@airchat/shared';

export interface SignalingConfig {
  serverUrl: string;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  connectionTimeout?: number;
}

export interface SignalingClientEvents {
  'connected': void;
  'disconnected': { reason?: string };
  'error': { error: SignalingError };
  'offer': { from: string; offer: RTCSessionDescriptionInit };
  'answer': { from: string; answer: RTCSessionDescriptionInit };
  'ice-candidate': { from: string; candidate: RTCIceCandidateInit };
  'peer-joined': { peerId: string; room: string };
  'peer-left': { peerId: string; room: string };
  'presence-update': { from: string; status: string; lastSeen?: number };
  'room-joined': { room: string };
  'room-left': { room: string };
}

export class SignalingClient extends EventTarget {
  private config: Required<SignalingConfig>;
  private ws: WebSocket | null = null;
  private peerId: string;
  private currentRoom: string | null = null;
  private reconnectAttempts = 0;
  private heartbeatTimer: number | null = null;
  private connectionTimer: number | null = null;
  private isConnecting = false;
  private isDestroyed = false;

  constructor(peerId: string, config: Partial<SignalingConfig> = {}) {
    super();
    
    this.peerId = peerId;
    this.config = {
      serverUrl: 'wss://airchat-signaling.your-domain.workers.dev',
      reconnectDelay: 1000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      connectionTimeout: 10000,
      ...config
    };
  }

  async connect(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('SignalingClient has been destroyed');
    }

    if (this.isConnecting || this.isConnected()) {
      return;
    }

    this.isConnecting = true;
    console.log(`Connecting to signaling server: ${this.config.serverUrl}`);

    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.serverUrl);
        
        // Set connection timeout
        this.connectionTimer = window.setTimeout(() => {
          if (this.isConnecting) {
            this.ws?.close();
            reject(new Error('Connection timeout'));
          }
        }, this.config.connectionTimeout);

        this.ws.onopen = () => {
          console.log('Connected to signaling server');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }

          this.startHeartbeat();
          this.emit('connected', undefined);
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          console.log('Disconnected from signaling server:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          
          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }

          this.emit('disconnected', { reason: event.reason });

          if (!this.isDestroyed && event.code !== 1000) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('Signaling WebSocket error:', error);
          this.isConnecting = false;
          
          if (this.connectionTimer) {
            clearTimeout(this.connectionTimer);
            this.connectionTimer = null;
          }

          this.emit('error', { 
            error: { 
              code: 'WEBSOCKET_ERROR', 
              message: 'WebSocket connection error',
              details: error
            } 
          });
          
          if (this.isConnecting) {
            reject(error);
          }
        };

      } catch (error) {
        this.isConnecting = false;
        if (this.connectionTimer) {
          clearTimeout(this.connectionTimer);
          this.connectionTimer = null;
        }
        reject(error);
      }
    });
  }

  disconnect(): void {
    console.log('Disconnecting from signaling server');
    this.isDestroyed = true;
    this.stopHeartbeat();
    
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  async joinRoom(roomId: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to signaling server');
    }

    const message: JoinRoomMessage = {
      type: 'join-room',
      from: this.peerId,
      room: roomId,
      timestamp: Date.now()
    };

    this.send(message);
    this.currentRoom = roomId;
  }

  async leaveRoom(): Promise<void> {
    if (!this.isConnected() || !this.currentRoom) {
      return;
    }

    const message: LeaveRoomMessage = {
      type: 'leave-room',
      from: this.peerId,
      room: this.currentRoom,
      timestamp: Date.now()
    };

    this.send(message);
    this.currentRoom = null;
  }

  async sendOffer(to: string, offer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to signaling server');
    }

    const message: OfferMessage = {
      type: 'offer',
      from: this.peerId,
      to,
      offer: {
        type: 'offer',
        sdp: offer.sdp!
      },
      timestamp: Date.now()
    };

    this.send(message);
  }

  async sendAnswer(to: string, answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to signaling server');
    }

    const message: AnswerMessage = {
      type: 'answer',
      from: this.peerId,
      to,
      answer: {
        type: 'answer',
        sdp: answer.sdp!
      },
      timestamp: Date.now()
    };

    this.send(message);
  }

  async sendIceCandidate(to: string, candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to signaling server');
    }

    const message: IceCandidateMessage = {
      type: 'ice-candidate',
      from: this.peerId,
      to,
      candidate: {
        candidate: candidate.candidate!,
        sdpMLineIndex: candidate.sdpMLineIndex!,
        sdpMid: candidate.sdpMid!
      },
      timestamp: Date.now()
    };

    this.send(message);
  }

  async updatePresence(status: 'online' | 'offline' | 'away'): Promise<void> {
    if (!this.isConnected()) {
      return;
    }

    const message: PresenceUpdateMessage = {
      type: 'presence-update',
      from: this.peerId,
      status,
      lastSeen: status === 'offline' ? Date.now() : undefined,
      timestamp: Date.now()
    };

    this.send(message);
  }

  private send(message: AnySignalingMessage): void {
    if (!this.isConnected()) {
      throw new Error('Not connected to signaling server');
    }

    try {
      this.ws!.send(JSON.stringify(message));
    } catch (error) {
      console.error('Failed to send signaling message:', error);
      this.emit('error', {
        error: {
          code: 'SEND_ERROR',
          message: 'Failed to send message',
          details: error
        }
      });
    }
  }

  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data) as AnySignalingMessage;
      
      switch (message.type) {
        case 'offer':
          this.emit('offer', {
            from: message.from,
            offer: message.offer
          });
          break;

        case 'answer':
          this.emit('answer', {
            from: message.from,
            answer: message.answer
          });
          break;

        case 'ice-candidate':
          this.emit('ice-candidate', {
            from: message.from,
            candidate: message.candidate
          });
          break;

        case 'peer-joined':
          this.emit('peer-joined', {
            peerId: message.peerId,
            room: message.room!
          });
          break;

        case 'peer-left':
          this.emit('peer-left', {
            peerId: message.peerId,
            room: message.room!
          });
          break;

        case 'presence-update':
          this.emit('presence-update', {
            from: message.from,
            status: message.status,
            lastSeen: message.lastSeen
          });
          break;

        default:
          console.warn('Unknown signaling message type:', message);
      }
    } catch (error) {
      console.error('Failed to parse signaling message:', error);
      this.emit('error', {
        error: {
          code: 'PARSE_ERROR',
          message: 'Failed to parse message',
          details: error
        }
      });
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        try {
          this.ws!.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
        } catch (error) {
          console.error('Failed to send heartbeat:', error);
        }
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private scheduleReconnect(): void {
    if (this.isDestroyed || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached or client destroyed');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(async () => {
      if (!this.isDestroyed) {
        try {
          await this.connect();
          
          // Rejoin room if we were in one
          if (this.currentRoom) {
            await this.joinRoom(this.currentRoom);
          }
        } catch (error) {
          console.error('Reconnect failed:', error);
        }
      }
    }, delay);
  }

  private emit<K extends keyof SignalingClientEvents>(
    type: K,
    detail: SignalingClientEvents[K]
  ): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }

  getCurrentRoom(): string | null {
    return this.currentRoom;
  }

  getPeerId(): string {
    return this.peerId;
  }
}
