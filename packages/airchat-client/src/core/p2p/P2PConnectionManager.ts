import type { ConnectionState, IceConnectionState } from '@airchat/shared';
import {
  TurnServerManager,
  type TurnServerProvider,
} from './TurnServerManager';

export interface P2PConnectionConfig {
  iceServers?: RTCIceServer[];
  iceCandidatePoolSize?: number;
  bundlePolicy?: RTCBundlePolicy;
  rtcpMuxPolicy?: RTCRtcpMuxPolicy;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  connectionTimeout?: number;
  keepAliveInterval?: number;
  turnProviders?: TurnServerProvider[];
  enableTurnFallback?: boolean;
  networkTestInterval?: number;
}

export interface DataChannelConfig {
  ordered?: boolean;
  maxRetransmits?: number;
  maxPacketLifeTime?: number;
}

export interface ConnectionInfo {
  peerId: string;
  connection: RTCPeerConnection;
  dataChannels: Map<string, RTCDataChannel>;
  state: ConnectionState;
  iceState: IceConnectionState;
  reconnectAttempts: number;
  lastActivity: number;
  connectionQuality: ConnectionQuality;
  keepAliveTimer?: NodeJS.Timeout;
  connectionTimer?: NodeJS.Timeout;
}

export interface ConnectionQuality {
  rtt: number; // Round trip time in ms
  packetsLost: number;
  packetsSent: number;
  bytesReceived: number;
  bytesSent: number;
  lastStatsUpdate: number;
}

export class P2PConnectionManager extends EventTarget {
  private config: Required<Omit<P2PConnectionConfig, 'turnProviders'>> & {
    turnProviders?: TurnServerProvider[];
  };
  private connections = new Map<string, ConnectionInfo>();
  private messageQueues = new Map<
    string,
    Array<{ channel: string; data: unknown }>
  >();
  private turnManager: TurnServerManager;

  constructor(config: Partial<P2PConnectionConfig> = {}) {
    super();

    this.config = {
      iceServers: config.iceServers || [],
      iceCandidatePoolSize: 10,
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require',
      maxReconnectAttempts: 5,
      reconnectDelay: 1000,
      connectionTimeout: 30000, // 30 seconds
      keepAliveInterval: 25000, // 25 seconds
      enableTurnFallback: true,
      networkTestInterval: 300000, // 5 minutes
      ...config,
      turnProviders: config.turnProviders,
    };

    // Initialize TURN server manager
    this.turnManager = new TurnServerManager();

    // Add custom TURN providers if specified
    if (config.turnProviders) {
      config.turnProviders.forEach(provider => {
        this.turnManager.addTurnProvider(provider);
      });
    }

    // Start quality monitoring
    this.startQualityMonitoring();
  }

  async createConnection(
    peerId: string,
    isInitiator: boolean = false
  ): Promise<RTCPeerConnection> {
    console.log(`Creating connection to ${peerId}, initiator: ${isInitiator}`);

    // Clean up existing connection if any
    if (this.connections.has(peerId)) {
      await this.closeConnection(peerId);
    }

    try {
      // Get dynamic ICE servers including TURN servers if needed
      let iceServers: RTCIceServer[];
      try {
        iceServers = await this.getOptimalIceServers();
      } catch (error) {
        console.error(
          'Failed to get optimal ICE servers, using fallback:',
          error
        );
        // Fallback to basic STUN servers
        iceServers = [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
          { urls: 'stun:stun.services.mozilla.com:3478' },
        ];
      }

      const pc = new RTCPeerConnection({
        iceServers,
        iceCandidatePoolSize: this.config.iceCandidatePoolSize,
        bundlePolicy: this.config.bundlePolicy,
        rtcpMuxPolicy: this.config.rtcpMuxPolicy,
      });

      const connectionInfo: ConnectionInfo = {
        peerId,
        connection: pc,
        dataChannels: new Map(),
        state: 'connecting',
        iceState: 'new',
        reconnectAttempts: 0,
        lastActivity: Date.now(),
        connectionQuality: {
          rtt: 0,
          packetsLost: 0,
          packetsSent: 0,
          bytesReceived: 0,
          bytesSent: 0,
          lastStatsUpdate: Date.now(),
        },
      };

      this.connections.set(peerId, connectionInfo);
      this.setupConnectionEventHandlers(pc, peerId);

      if (isInitiator) {
        await this.createDataChannels(pc, peerId);
      } else {
        this.setupDataChannelListener(pc, peerId);
      }

      this.emit('connection-created', { peerId, isInitiator });
      return pc;
    } catch (error) {
      console.error('Failed to create connection:', error);
      this.emit('connection-error', { peerId, error });
      throw error;
    }
  }

  private async createDataChannels(
    pc: RTCPeerConnection,
    peerId: string
  ): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    // Create message channel
    const messageChannel = pc.createDataChannel('messages', {
      ordered: true,
      maxRetransmits: 3,
    });

    // Create file transfer channel
    const fileChannel = pc.createDataChannel('files', {
      ordered: false,
      maxPacketLifeTime: 3000,
    });

    this.setupDataChannelHandlers(messageChannel, peerId, 'messages');
    this.setupDataChannelHandlers(fileChannel, peerId, 'files');

    connectionInfo.dataChannels.set('messages', messageChannel);
    connectionInfo.dataChannels.set('files', fileChannel);
  }

  private setupDataChannelListener(
    pc: RTCPeerConnection,
    peerId: string
  ): void {
    pc.ondatachannel = event => {
      const channel = event.channel;
      const connectionInfo = this.connections.get(peerId);

      if (connectionInfo) {
        this.setupDataChannelHandlers(channel, peerId, channel.label);
        connectionInfo.dataChannels.set(channel.label, channel);
      }
    };
  }

  private setupConnectionEventHandlers(
    pc: RTCPeerConnection,
    peerId: string
  ): void {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    pc.onconnectionstatechange = () => {
      const state = pc.connectionState as ConnectionState;
      connectionInfo.state = state;
      connectionInfo.lastActivity = Date.now();

      console.log(`Connection to ${peerId}: ${state}`);
      this.emit('connection-state-change', { peerId, state });

      switch (state) {
        case 'connected':
          connectionInfo.reconnectAttempts = 0;
          this.processQueuedMessages(peerId);
          this.emit('peer-connected', { peerId });
          break;

        case 'disconnected':
          this.emit('peer-disconnected', { peerId });
          this.scheduleReconnect(peerId);
          break;

        case 'failed':
          this.emit('peer-failed', { peerId });
          this.handleConnectionFailure(peerId);
          break;

        case 'closed':
          this.cleanupPeer(peerId);
          break;
      }
    };

    pc.oniceconnectionstatechange = () => {
      const iceState = pc.iceConnectionState as IceConnectionState;
      connectionInfo.iceState = iceState;
      console.log(`ICE connection to ${peerId}: ${iceState}`);
      this.emit('ice-connection-state-change', { peerId, iceState });
    };

    pc.onicegatheringstatechange = () => {
      console.log(`ICE gathering for ${peerId}: ${pc.iceGatheringState}`);
      this.emit('ice-gathering-state-change', {
        peerId,
        state: pc.iceGatheringState,
      });
    };

    pc.onicecandidate = event => {
      if (event.candidate) {
        this.emit('ice-candidate', {
          peerId,
          candidate: event.candidate,
        });
      }
    };

    pc.onicecandidateerror = event => {
      console.error('ICE candidate error:', event);
      this.emit('ice-error', { peerId, error: event });
    };
  }

  private setupDataChannelHandlers(
    channel: RTCDataChannel,
    peerId: string,
    channelType: string
  ): void {
    channel.onopen = () => {
      console.log(`${channelType} channel opened for ${peerId}`);
      this.emit('channel-opened', { peerId, channelType });
      this.processQueuedMessages(peerId);
    };

    channel.onmessage = event => {
      const connectionInfo = this.connections.get(peerId);
      if (connectionInfo) {
        connectionInfo.lastActivity = Date.now();
      }

      this.handleChannelMessage(peerId, channelType, event.data);
    };

    channel.onerror = error => {
      console.error(`${channelType} channel error:`, error);
      this.emit('channel-error', { peerId, channelType, error });
    };

    channel.onclose = () => {
      console.log(`${channelType} channel closed for ${peerId}`);
      const connectionInfo = this.connections.get(peerId);
      if (connectionInfo) {
        connectionInfo.dataChannels.delete(channelType);
      }
      this.emit('channel-closed', { peerId, channelType });
    };
  }

  private handleChannelMessage(
    peerId: string,
    channelType: string,
    data: unknown
  ): void {
    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data;
      this.emit('message-received', {
        peerId,
        channelType,
        message,
      });
    } catch (error) {
      console.error('Failed to parse channel message:', error);
      this.emit('message-parse-error', { peerId, channelType, data, error });
    }
  }

  async sendMessage(
    peerId: string,
    message: unknown,
    channelType: string = 'messages'
  ): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) {
      throw new Error(`No connection to peer ${peerId}`);
    }

    const channel = connectionInfo.dataChannels.get(channelType);

    if (channel && channel.readyState === 'open') {
      try {
        const data = JSON.stringify(message);
        channel.send(data);
        connectionInfo.lastActivity = Date.now();
        this.emit('message-sent', { peerId, channelType, message });
      } catch (error) {
        console.error('Failed to send message:', error);
        this.emit('message-send-error', {
          peerId,
          channelType,
          message,
          error,
        });
        throw error;
      }
    } else {
      // Queue message for later delivery
      this.queueMessage(peerId, channelType, message);
    }
  }

  private queueMessage(
    peerId: string,
    channelType: string,
    data: unknown
  ): void {
    if (!this.messageQueues.has(peerId)) {
      this.messageQueues.set(peerId, []);
    }

    const queue = this.messageQueues.get(peerId)!;
    queue.push({ channel: channelType, data });

    console.log(`Queued message for ${peerId} on ${channelType} channel`);
  }

  private processQueuedMessages(peerId: string): void {
    const queue = this.messageQueues.get(peerId);
    if (!queue || queue.length === 0) return;

    console.log(`Processing ${queue.length} queued messages for ${peerId}`);

    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    const processedMessages: Array<{ channel: string; data: unknown }> = [];

    for (const queuedMessage of queue) {
      const channel = connectionInfo.dataChannels.get(queuedMessage.channel);

      if (channel && channel.readyState === 'open') {
        try {
          const data = JSON.stringify(queuedMessage.data);
          channel.send(data);
          processedMessages.push(queuedMessage);
        } catch (error) {
          console.error('Failed to send queued message:', error);
          break; // Stop processing on error
        }
      }
    }

    // Remove processed messages from queue
    if (processedMessages.length > 0) {
      const remainingMessages = queue.filter(
        msg => !processedMessages.includes(msg)
      );
      this.messageQueues.set(peerId, remainingMessages);
    }
  }

  private async scheduleReconnect(peerId: string): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    if (connectionInfo.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.log(`Max reconnect attempts reached for ${peerId}`);
      this.emit('peer-unreachable', { peerId });
      return;
    }

    connectionInfo.reconnectAttempts++;
    const delay =
      this.config.reconnectDelay *
      Math.pow(2, connectionInfo.reconnectAttempts - 1);

    console.log(
      `Scheduling reconnect to ${peerId} in ${delay}ms (attempt ${connectionInfo.reconnectAttempts})`
    );

    setTimeout(async () => {
      try {
        await this.reconnect(peerId);
      } catch (error) {
        console.error(`Reconnect failed:`, error);
        this.emit('reconnect-failed', { peerId, error });
      }
    }, delay);
  }

  private async reconnect(peerId: string): Promise<void> {
    console.log(`Attempting to reconnect to ${peerId}`);
    this.emit('reconnecting', { peerId });

    // The actual reconnection logic would be handled by the signaling layer
    // This method primarily manages the reconnection state
  }

  private async handleConnectionFailure(peerId: string): Promise<void> {
    console.log(`Handling connection failure for ${peerId}`);
    await this.scheduleReconnect(peerId);
  }

  async closeConnection(peerId: string): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    console.log(`Closing connection to ${peerId}`);

    // Close all data channels
    for (const [, channel] of connectionInfo.dataChannels) {
      if (channel.readyState !== 'closed') {
        channel.close();
      }
    }

    // Close the peer connection
    if (connectionInfo.connection.connectionState !== 'closed') {
      connectionInfo.connection.close();
    }

    this.cleanupPeer(peerId);
  }

  private cleanupPeer(peerId: string): void {
    this.connections.delete(peerId);
    this.messageQueues.delete(peerId);
    this.emit('peer-cleanup', { peerId });
  }

  getConnection(peerId: string): RTCPeerConnection | undefined {
    return this.connections.get(peerId)?.connection;
  }

  getConnectionInfo(peerId: string): ConnectionInfo | undefined {
    return this.connections.get(peerId);
  }

  getAllConnections(): Map<string, ConnectionInfo> {
    return new Map(this.connections);
  }

  isConnected(peerId: string): boolean {
    const connectionInfo = this.connections.get(peerId);
    return (
      connectionInfo?.state === 'connected' &&
      connectionInfo.dataChannels.size > 0
    );
  }

  private emit(type: string, detail: unknown): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }

  /**
   * Get connection quality metrics for a peer
   */
  getConnectionQuality(peerId: string): ConnectionQuality | undefined {
    return this.connections.get(peerId)?.connectionQuality;
  }

  /**
   * Start monitoring connection quality for all peers
   */
  private startQualityMonitoring(): void {
    setInterval(() => {
      for (const [peerId, connectionInfo] of this.connections) {
        if (connectionInfo.state === 'connected') {
          this.updateConnectionStats(peerId);
        }
      }
    }, 5000); // Update every 5 seconds
  }

  /**
   * Update connection statistics for a peer
   */
  private async updateConnectionStats(peerId: string): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) return;

    try {
      const stats = await connectionInfo.connection.getStats();
      let rtt = 0;
      let packetsLost = 0;
      let packetsSent = 0;
      let bytesReceived = 0;
      let bytesSent = 0;

      stats.forEach(report => {
        if (report.type === 'candidate-pair' && report.state === 'succeeded') {
          rtt = report.currentRoundTripTime * 1000 || 0; // Convert to ms
        } else if (report.type === 'outbound-rtp') {
          packetsSent += report.packetsSent || 0;
          bytesSent += report.bytesSent || 0;
        } else if (report.type === 'inbound-rtp') {
          packetsLost += report.packetsLost || 0;
          bytesReceived += report.bytesReceived || 0;
        }
      });

      connectionInfo.connectionQuality = {
        rtt,
        packetsLost,
        packetsSent,
        bytesReceived,
        bytesSent,
        lastStatsUpdate: Date.now(),
      };

      // Emit quality update event
      this.emit('connection-quality-update', {
        peerId,
        quality: connectionInfo.connectionQuality,
      });

      // Check for poor connection quality
      if (rtt > 1000 || packetsLost / Math.max(packetsSent, 1) > 0.1) {
        this.emit('connection-quality-poor', {
          peerId,
          quality: connectionInfo.connectionQuality,
        });
      }
    } catch (error) {
      console.error(`Failed to get stats for peer ${peerId}:`, error);
    }
  }

  /**
   * Get optimal ICE servers based on network conditions
   */
  private async getOptimalIceServers(): Promise<RTCIceServer[]> {
    try {
      // Get ICE servers from TURN manager (includes STUN + TURN if needed)
      const dynamicIceServers = await this.turnManager.getIceServers();

      // Combine with any manually configured ICE servers
      const configuredServers = this.config.iceServers || [];

      // Remove duplicates and return combined list
      const allServers = [...configuredServers, ...dynamicIceServers];
      const uniqueServers = this.deduplicateIceServers(allServers);

      console.log(`Using ${uniqueServers.length} ICE servers for connection`);
      return uniqueServers;
    } catch (error) {
      console.error(
        'Failed to get optimal ICE servers, falling back to basic STUN:',
        error
      );

      // Fallback to basic STUN servers
      return [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun.services.mozilla.com:3478' },
      ];
    }
  }

  /**
   * Remove duplicate ICE servers
   */
  private deduplicateIceServers(servers: RTCIceServer[]): RTCIceServer[] {
    const seen = new Set<string>();
    return servers.filter(server => {
      const key = Array.isArray(server.urls)
        ? server.urls.join(',')
        : server.urls;
      const fullKey = `${key}:${server.username || ''}`;

      if (seen.has(fullKey)) {
        return false;
      }
      seen.add(fullKey);
      return true;
    });
  }

  /**
   * Test network connectivity and update TURN requirements
   */
  async testNetworkConnectivity(): Promise<void> {
    try {
      const result = await this.turnManager.testNetworkConnectivity();
      this.emit('network-connectivity-tested', { result });

      if (result.requiresTurn && this.config.enableTurnFallback) {
        console.log('Network requires TURN servers for reliable connectivity');
        this.emit('turn-required', { result });
      }
    } catch (error) {
      console.error('Network connectivity test failed:', error);
      this.emit('network-test-error', { error });
    }
  }

  /**
   * Get current network connectivity status
   */
  getNetworkConnectivityStatus() {
    return this.turnManager.getConnectivityStatus();
  }

  /**
   * Add a TURN server provider
   */
  addTurnProvider(provider: TurnServerProvider): void {
    this.turnManager.addTurnProvider(provider);
  }

  /**
   * Remove a TURN server provider
   */
  removeTurnProvider(providerName: string): void {
    this.turnManager.removeTurnProvider(providerName);
  }

  /**
   * Clean up all connections and resources
   */
  cleanup(): void {
    const peerIds = Array.from(this.connections.keys());
    for (const peerId of peerIds) {
      this.closeConnection(peerId);
    }
    this.connections.clear();

    // Clean up TURN manager
    this.turnManager.destroy();
  }
}
