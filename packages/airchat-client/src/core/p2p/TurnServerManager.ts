/**
 * TURN Server Management for Production P2P Connections
 *
 * Handles TURN server configuration, credential management, and fallback logic
 * for reliable P2P connections in restrictive network environments.
 */

export interface TurnServerConfig {
  urls: string | string[];
  username?: string;
  credential?: string;
  credentialType?: RTCIceCredentialType;
  priority?: number;
  region?: string;
  provider?: string;
}

export interface TurnCredentials {
  username: string;
  credential: string;
  ttl: number; // Time to live in seconds
  expiresAt: number; // Unix timestamp
}

export interface TurnServerProvider {
  name: string;
  servers: TurnServerConfig[];
  credentialEndpoint?: string;
  apiKey?: string;
  region?: string;
}

export interface NetworkConnectivityResult {
  canConnectDirectly: boolean;
  natType:
    | 'none'
    | 'full-cone'
    | 'restricted'
    | 'port-restricted'
    | 'symmetric';
  requiresTurn: boolean;
  stunWorking: boolean;
  firewallRestrictions: string[];
  recommendedServers: TurnServerConfig[];
}

export class TurnServerManager extends EventTarget {
  private turnProviders: Map<string, TurnServerProvider> = new Map();
  private credentialCache: Map<string, TurnCredentials> = new Map();
  private connectivityCache: NetworkConnectivityResult | null = null;
  private connectivityTestTimer: number | null = null;
  private credentialRefreshTimer: number | null = null;

  constructor() {
    super();
    this.initializeDefaultProviders();
    this.startPeriodicConnectivityTests();
  }

  /**
   * Initialize default TURN server providers
   */
  private initializeDefaultProviders(): void {
    // Cloudflare TURN (if available)
    this.addTurnProvider({
      name: 'cloudflare',
      servers: [
        {
          urls: 'turn:turn.cloudflare.com:3478',
          priority: 1,
          region: 'global',
          provider: 'cloudflare',
        },
      ],
    });

    // Google TURN (if available)
    this.addTurnProvider({
      name: 'google',
      servers: [
        {
          urls: 'turn:global.turn.twilio.com:3478',
          priority: 2,
          region: 'global',
          provider: 'twilio',
        },
      ],
    });

    // Custom TURN servers (configurable)
    this.addTurnProvider({
      name: 'custom',
      servers: [
        {
          urls: 'turn:your-turn-server.com:3478',
          priority: 3,
          region: 'custom',
          provider: 'custom',
        },
      ],
    });
  }

  /**
   * Add a TURN server provider
   */
  addTurnProvider(provider: TurnServerProvider): void {
    this.turnProviders.set(provider.name, provider);
    this.emit('provider-added', { provider: provider.name });
  }

  /**
   * Remove a TURN server provider
   */
  removeTurnProvider(providerName: string): void {
    this.turnProviders.delete(providerName);
    this.credentialCache.delete(providerName);
    this.emit('provider-removed', { provider: providerName });
  }

  /**
   * Test network connectivity and determine TURN requirements
   */
  async testNetworkConnectivity(): Promise<NetworkConnectivityResult> {
    console.log('Testing network connectivity...');

    try {
      const result: NetworkConnectivityResult = {
        canConnectDirectly: false,
        natType: 'none',
        requiresTurn: false,
        stunWorking: false,
        firewallRestrictions: [],
        recommendedServers: [],
      };

      // Test STUN connectivity
      const stunResult = await this.testStunConnectivity();
      result.stunWorking = stunResult.working;
      result.natType = stunResult.natType;

      // Test direct connectivity
      const directResult = await this.testDirectConnectivity();
      result.canConnectDirectly = directResult;

      // Determine if TURN is required
      result.requiresTurn =
        !result.canConnectDirectly ||
        result.natType === 'symmetric' ||
        !result.stunWorking;

      // Detect firewall restrictions
      result.firewallRestrictions = await this.detectFirewallRestrictions();

      // Recommend appropriate servers
      result.recommendedServers = this.getRecommendedServers(result);

      this.connectivityCache = result;
      this.emit('connectivity-tested', { result });

      return result;
    } catch (error) {
      console.error('Network connectivity test failed:', error);
      throw error;
    }
  }

  /**
   * Test STUN server connectivity and NAT type detection
   */
  private async testStunConnectivity(): Promise<{
    working: boolean;
    natType: string;
  }> {
    return new Promise(resolve => {
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
        ],
      });

      const candidates: RTCIceCandidate[] = [];
      let timeout: number;

      pc.onicecandidate = event => {
        if (event.candidate) {
          candidates.push(event.candidate);
        } else {
          // ICE gathering complete
          clearTimeout(timeout);
          pc.close();

          const natType = this.analyzeNatType(candidates);
          resolve({
            working: candidates.length > 0,
            natType,
          });
        }
      };

      // Create a data channel to trigger ICE gathering
      pc.createDataChannel('test');
      pc.createOffer().then(offer => pc.setLocalDescription(offer));

      // Timeout after 10 seconds
      timeout = window.setTimeout(() => {
        pc.close();
        resolve({ working: false, natType: 'unknown' });
      }, 10000);
    });
  }

  /**
   * Analyze NAT type based on ICE candidates
   */
  private analyzeNatType(candidates: RTCIceCandidate[]): string {
    const hostCandidates = candidates.filter(c => c.type === 'host');
    const srflxCandidates = candidates.filter(c => c.type === 'srflx');
    const relayCandidates = candidates.filter(c => c.type === 'relay');

    if (hostCandidates.length > 0 && srflxCandidates.length === 0) {
      return 'none'; // No NAT
    }

    if (srflxCandidates.length > 0) {
      // Check if multiple server reflexive candidates have different ports
      const ports = new Set(srflxCandidates.map(c => c.port));
      if (ports.size > 1) {
        return 'symmetric';
      }
      return 'full-cone'; // Simplified - could be more specific
    }

    return 'unknown';
  }

  /**
   * Test direct P2P connectivity
   */
  private async testDirectConnectivity(): Promise<boolean> {
    // This would typically involve attempting a connection to a test peer
    // For now, we'll use a heuristic based on network conditions
    return new Promise(resolve => {
      // Simulate connectivity test
      setTimeout(() => {
        // In a real implementation, this would test actual P2P connectivity
        const hasPublicIP = this.hasPublicIP();
        resolve(hasPublicIP);
      }, 2000);
    });
  }

  /**
   * Check if the client has a public IP address
   */
  private hasPublicIP(): boolean {
    // This is a simplified check - in reality, you'd need to detect the actual IP
    if (typeof window === 'undefined' || !window.location) {
      return false; // Assume private in test/server environments
    }
    return !this.isPrivateIP(window.location.hostname);
  }

  /**
   * Check if an IP address is private
   */
  private isPrivateIP(ip: string): boolean {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^localhost$/,
    ];

    return privateRanges.some(range => range.test(ip));
  }

  /**
   * Detect firewall restrictions
   */
  private async detectFirewallRestrictions(): Promise<string[]> {
    const restrictions: string[] = [];

    // Test common ports
    const testPorts = [80, 443, 3478, 5349];
    for (const port of testPorts) {
      const isBlocked = await this.testPortConnectivity(port);
      if (isBlocked) {
        restrictions.push(`Port ${port} blocked`);
      }
    }

    // Test UDP vs TCP
    const udpBlocked = await this.testUdpConnectivity();
    if (udpBlocked) {
      restrictions.push('UDP traffic blocked');
    }

    return restrictions;
  }

  /**
   * Test connectivity to a specific port
   */
  private async testPortConnectivity(port: number): Promise<boolean> {
    // Simplified test - in reality, you'd test actual connectivity
    return Promise.resolve(false); // Assume ports are open for now
  }

  /**
   * Test UDP connectivity
   */
  private async testUdpConnectivity(): Promise<boolean> {
    // Simplified test - in reality, you'd test UDP specifically
    return Promise.resolve(false); // Assume UDP is available for now
  }

  /**
   * Get recommended TURN servers based on connectivity results
   */
  private getRecommendedServers(
    connectivity: NetworkConnectivityResult
  ): TurnServerConfig[] {
    const recommended: TurnServerConfig[] = [];

    if (!connectivity.requiresTurn) {
      return recommended;
    }

    // Sort providers by priority and add their servers
    const sortedProviders = Array.from(this.turnProviders.values()).sort(
      (a, b) => {
        const aPriority = Math.min(...a.servers.map(s => s.priority || 999));
        const bPriority = Math.min(...b.servers.map(s => s.priority || 999));
        return aPriority - bPriority;
      }
    );

    for (const provider of sortedProviders) {
      recommended.push(...provider.servers);
    }

    return recommended;
  }

  /**
   * Get ICE servers configuration including TURN servers with credentials
   */
  async getIceServers(): Promise<RTCIceServer[]> {
    const iceServers: RTCIceServer[] = [];

    // Always include STUN servers
    iceServers.push(
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun.services.mozilla.com:3478' },
      { urls: 'stun:stun.cloudflare.com:3478' }
    );

    // Test connectivity if not cached or cache is old
    if (
      !this.connectivityCache ||
      Date.now() - (this.connectivityCache as any).timestamp > 300000
    ) {
      // 5 minutes
      await this.testNetworkConnectivity();
    }

    // Add TURN servers if required
    if (this.connectivityCache?.requiresTurn) {
      const turnServers = await this.getTurnServersWithCredentials();
      iceServers.push(...turnServers);
    }

    return iceServers;
  }

  /**
   * Get TURN servers with valid credentials
   */
  private async getTurnServersWithCredentials(): Promise<RTCIceServer[]> {
    const turnServers: RTCIceServer[] = [];

    for (const [providerName, provider] of this.turnProviders) {
      try {
        const credentials = await this.getValidCredentials(
          providerName,
          provider
        );

        for (const server of provider.servers) {
          turnServers.push({
            urls: server.urls,
            username: credentials?.username || server.username,
            credential: credentials?.credential || server.credential,
            credentialType: server.credentialType || 'password',
          });
        }
      } catch (error) {
        console.warn(`Failed to get credentials for ${providerName}:`, error);
      }
    }

    return turnServers;
  }

  /**
   * Get valid credentials for a provider (with caching and refresh)
   */
  private async getValidCredentials(
    providerName: string,
    provider: TurnServerProvider
  ): Promise<TurnCredentials | null> {
    // Check cache first
    const cached = this.credentialCache.get(providerName);
    if (cached && cached.expiresAt > Date.now()) {
      return cached;
    }

    // Refresh credentials if endpoint is available
    if (provider.credentialEndpoint && provider.apiKey) {
      try {
        const newCredentials = await this.refreshCredentials(provider);
        this.credentialCache.set(providerName, newCredentials);
        return newCredentials;
      } catch (error) {
        console.error(
          `Failed to refresh credentials for ${providerName}:`,
          error
        );
      }
    }

    return null;
  }

  /**
   * Refresh credentials from provider endpoint
   */
  private async refreshCredentials(
    provider: TurnServerProvider
  ): Promise<TurnCredentials> {
    if (!provider.credentialEndpoint || !provider.apiKey) {
      throw new Error('Credential endpoint or API key not configured');
    }

    const response = await fetch(provider.credentialEndpoint, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ttl: 3600, // Request 1 hour TTL
      }),
    });

    if (!response.ok) {
      throw new Error(`Credential refresh failed: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      username: data.username,
      credential: data.credential,
      ttl: data.ttl || 3600,
      expiresAt: Date.now() + (data.ttl || 3600) * 1000,
    };
  }

  /**
   * Start periodic connectivity tests
   */
  private startPeriodicConnectivityTests(): void {
    // Test connectivity every 5 minutes
    this.connectivityTestTimer = window.setInterval(() => {
      this.testNetworkConnectivity().catch(error => {
        console.error('Periodic connectivity test failed:', error);
      });
    }, 300000);

    // Refresh credentials every 30 minutes
    this.credentialRefreshTimer = window.setInterval(() => {
      this.refreshAllCredentials().catch(error => {
        console.error('Credential refresh failed:', error);
      });
    }, 1800000);
  }

  /**
   * Refresh all cached credentials
   */
  private async refreshAllCredentials(): Promise<void> {
    const refreshPromises = Array.from(this.turnProviders.entries()).map(
      async ([name, provider]) => {
        try {
          if (provider.credentialEndpoint && provider.apiKey) {
            const credentials = await this.refreshCredentials(provider);
            this.credentialCache.set(name, credentials);
          }
        } catch (error) {
          console.error(`Failed to refresh credentials for ${name}:`, error);
        }
      }
    );

    await Promise.allSettled(refreshPromises);
  }

  /**
   * Get current connectivity status
   */
  getConnectivityStatus(): NetworkConnectivityResult | null {
    return this.connectivityCache;
  }

  /**
   * Destroy the manager and clean up resources
   */
  destroy(): void {
    if (this.connectivityTestTimer) {
      clearInterval(this.connectivityTestTimer);
      this.connectivityTestTimer = null;
    }

    if (this.credentialRefreshTimer) {
      clearInterval(this.credentialRefreshTimer);
      this.credentialRefreshTimer = null;
    }

    this.turnProviders.clear();
    this.credentialCache.clear();
    this.connectivityCache = null;
  }

  /**
   * Emit events
   */
  private emit(type: string, detail: any): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
