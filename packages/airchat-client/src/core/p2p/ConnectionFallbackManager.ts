/**
 * Connection Fallback Manager for Restrictive Networks
 * 
 * Implements intelligent fallback strategies for P2P connections
 * in challenging network environments including corporate firewalls,
 * symmetric NAT, and other restrictive scenarios.
 */

import type { TurnServerConfig, NetworkConnectivityResult } from './TurnServerManager';

export interface FallbackStrategy {
  name: string;
  priority: number;
  description: string;
  requirements: string[];
  iceServers: RTCIceServer[];
  timeout: number;
  maxRetries: number;
}

export interface FallbackAttempt {
  strategy: FallbackStrategy;
  startTime: number;
  endTime?: number;
  success: boolean;
  error?: string;
  connectionState?: RTCPeerConnectionState;
  iceConnectionState?: RTCIceConnectionState;
}

export interface FallbackResult {
  success: boolean;
  strategy?: FallbackStrategy;
  attempts: FallbackAttempt[];
  totalTime: number;
  finalConnection?: RTCPeerConnection;
}

export class ConnectionFallbackManager extends EventTarget {
  private strategies: FallbackStrategy[] = [];
  private currentAttempt: FallbackAttempt | null = null;
  private attemptHistory: FallbackAttempt[] = [];

  constructor() {
    super();
    this.initializeDefaultStrategies();
  }

  /**
   * Initialize default fallback strategies
   */
  private initializeDefaultStrategies(): void {
    // Strategy 1: Direct connection (no STUN/TURN)
    this.addStrategy({
      name: 'direct',
      priority: 1,
      description: 'Direct P2P connection without STUN/TURN servers',
      requirements: ['Public IP', 'No NAT'],
      iceServers: [],
      timeout: 10000,
      maxRetries: 1
    });

    // Strategy 2: STUN-only connection
    this.addStrategy({
      name: 'stun',
      priority: 2,
      description: 'P2P connection using STUN servers for NAT traversal',
      requirements: ['STUN server access', 'Non-symmetric NAT'],
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun.services.mozilla.com:3478' },
        { urls: 'stun:stun.cloudflare.com:3478' }
      ],
      timeout: 15000,
      maxRetries: 2
    });

    // Strategy 3: TURN over UDP
    this.addStrategy({
      name: 'turn-udp',
      priority: 3,
      description: 'TURN relay using UDP transport',
      requirements: ['TURN server access', 'UDP not blocked'],
      iceServers: [], // Will be populated dynamically
      timeout: 20000,
      maxRetries: 2
    });

    // Strategy 4: TURN over TCP
    this.addStrategy({
      name: 'turn-tcp',
      priority: 4,
      description: 'TURN relay using TCP transport',
      requirements: ['TURN server access', 'TCP port 3478 open'],
      iceServers: [], // Will be populated dynamically
      timeout: 25000,
      maxRetries: 2
    });

    // Strategy 5: TURN over TLS (port 443)
    this.addStrategy({
      name: 'turn-tls',
      priority: 5,
      description: 'TURN relay using TLS on port 443 (corporate firewall friendly)',
      requirements: ['TURN server with TLS support', 'HTTPS port 443 open'],
      iceServers: [], // Will be populated dynamically
      timeout: 30000,
      maxRetries: 3
    });

    // Strategy 6: WebSocket relay fallback
    this.addStrategy({
      name: 'websocket-relay',
      priority: 6,
      description: 'WebSocket-based relay for extremely restrictive networks',
      requirements: ['WebSocket support', 'HTTPS access'],
      iceServers: [],
      timeout: 35000,
      maxRetries: 3
    });
  }

  /**
   * Add a fallback strategy
   */
  addStrategy(strategy: FallbackStrategy): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.priority - b.priority);
    this.emit('strategy-added', { strategy });
  }

  /**
   * Remove a fallback strategy
   */
  removeStrategy(name: string): void {
    const index = this.strategies.findIndex(s => s.name === name);
    if (index !== -1) {
      const removed = this.strategies.splice(index, 1)[0];
      this.emit('strategy-removed', { strategy: removed });
    }
  }

  /**
   * Update TURN servers for TURN-based strategies
   */
  updateTurnServers(turnServers: TurnServerConfig[]): void {
    const turnStrategies = this.strategies.filter(s => s.name.startsWith('turn-'));
    
    for (const strategy of turnStrategies) {
      strategy.iceServers = this.buildIceServersForStrategy(strategy, turnServers);
    }

    this.emit('turn-servers-updated', { turnServers });
  }

  /**
   * Build ICE servers configuration for a specific strategy
   */
  private buildIceServersForStrategy(
    strategy: FallbackStrategy, 
    turnServers: TurnServerConfig[]
  ): RTCIceServer[] {
    const iceServers: RTCIceServer[] = [];

    // Always include STUN servers (except for direct strategy)
    if (strategy.name !== 'direct') {
      iceServers.push(
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      );
    }

    // Add appropriate TURN servers based on strategy
    for (const turnServer of turnServers) {
      const urls = Array.isArray(turnServer.urls) ? turnServer.urls : [turnServer.urls];
      
      for (const url of urls) {
        if (strategy.name === 'turn-udp' && url.includes('turn:') && !url.includes('tcp')) {
          iceServers.push({
            urls: url,
            username: turnServer.username,
            credential: turnServer.credential
          });
        } else if (strategy.name === 'turn-tcp' && url.includes('turn:') && url.includes('tcp')) {
          iceServers.push({
            urls: url,
            username: turnServer.username,
            credential: turnServer.credential
          });
        } else if (strategy.name === 'turn-tls' && (url.includes('turns:') || url.includes(':443'))) {
          iceServers.push({
            urls: url,
            username: turnServer.username,
            credential: turnServer.credential
          });
        }
      }
    }

    return iceServers;
  }

  /**
   * Attempt connection with automatic fallback
   */
  async attemptConnectionWithFallback(
    peerId: string,
    networkConnectivity: NetworkConnectivityResult,
    isInitiator: boolean = false
  ): Promise<FallbackResult> {
    const startTime = Date.now();
    const attempts: FallbackAttempt[] = [];
    
    console.log(`Starting fallback connection attempt for peer ${peerId}`);
    
    // Filter and sort strategies based on network conditions
    const applicableStrategies = this.getApplicableStrategies(networkConnectivity);
    
    for (const strategy of applicableStrategies) {
      console.log(`Attempting connection with strategy: ${strategy.name}`);
      
      const attempt = await this.attemptStrategy(strategy, peerId, isInitiator);
      attempts.push(attempt);
      
      if (attempt.success) {
        const result: FallbackResult = {
          success: true,
          strategy,
          attempts,
          totalTime: Date.now() - startTime,
          finalConnection: (attempt as any).connection
        };
        
        this.emit('fallback-success', { result, peerId });
        return result;
      }
      
      // Wait a bit before trying next strategy
      await this.delay(1000);
    }
    
    // All strategies failed
    const result: FallbackResult = {
      success: false,
      attempts,
      totalTime: Date.now() - startTime
    };
    
    this.emit('fallback-failed', { result, peerId });
    return result;
  }

  /**
   * Get strategies applicable to current network conditions
   */
  private getApplicableStrategies(connectivity: NetworkConnectivityResult): FallbackStrategy[] {
    return this.strategies.filter(strategy => {
      // Skip direct if NAT is present
      if (strategy.name === 'direct' && connectivity.natType !== 'none') {
        return false;
      }
      
      // Skip STUN-only for symmetric NAT
      if (strategy.name === 'stun' && connectivity.natType === 'symmetric') {
        return false;
      }
      
      // Skip UDP TURN if UDP is blocked
      if (strategy.name === 'turn-udp' && 
          connectivity.firewallRestrictions.includes('UDP traffic blocked')) {
        return false;
      }
      
      // Prefer TLS TURN for corporate firewalls
      if (connectivity.firewallRestrictions.includes('Corporate firewall detected')) {
        return strategy.name === 'turn-tls' || strategy.name === 'websocket-relay';
      }
      
      return true;
    });
  }

  /**
   * Attempt connection with a specific strategy
   */
  private async attemptStrategy(
    strategy: FallbackStrategy,
    peerId: string,
    isInitiator: boolean
  ): Promise<FallbackAttempt> {
    const attempt: FallbackAttempt = {
      strategy,
      startTime: Date.now(),
      success: false
    };

    this.currentAttempt = attempt;
    this.emit('strategy-attempt-start', { attempt, peerId });

    try {
      for (let retry = 0; retry < strategy.maxRetries; retry++) {
        console.log(`Strategy ${strategy.name}, attempt ${retry + 1}/${strategy.maxRetries}`);
        
        const connection = await this.createConnectionWithStrategy(strategy, peerId, isInitiator);
        
        if (connection) {
          attempt.success = true;
          attempt.connectionState = connection.connectionState;
          attempt.iceConnectionState = connection.iceConnectionState;
          (attempt as any).connection = connection;
          break;
        }
        
        // Wait before retry
        if (retry < strategy.maxRetries - 1) {
          await this.delay(2000);
        }
      }
    } catch (error) {
      attempt.error = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Strategy ${strategy.name} failed:`, error);
    }

    attempt.endTime = Date.now();
    this.currentAttempt = null;
    this.attemptHistory.push(attempt);
    
    this.emit('strategy-attempt-end', { attempt, peerId });
    
    return attempt;
  }

  /**
   * Create connection with specific strategy
   */
  private async createConnectionWithStrategy(
    strategy: FallbackStrategy,
    peerId: string,
    isInitiator: boolean
  ): Promise<RTCPeerConnection | null> {
    return new Promise((resolve) => {
      const pc = new RTCPeerConnection({
        iceServers: strategy.iceServers,
        iceCandidatePoolSize: 10,
        bundlePolicy: 'max-bundle',
        rtcpMuxPolicy: 'require'
      });

      let resolved = false;
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          pc.close();
          resolve(null);
        }
      }, strategy.timeout);

      // Monitor connection state
      pc.onconnectionstatechange = () => {
        if (resolved) return;
        
        if (pc.connectionState === 'connected') {
          resolved = true;
          clearTimeout(timeout);
          resolve(pc);
        } else if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
          resolved = true;
          clearTimeout(timeout);
          pc.close();
          resolve(null);
        }
      };

      // Monitor ICE connection state
      pc.oniceconnectionstatechange = () => {
        if (resolved) return;
        
        if (pc.iceConnectionState === 'connected' || pc.iceConnectionState === 'completed') {
          resolved = true;
          clearTimeout(timeout);
          resolve(pc);
        } else if (pc.iceConnectionState === 'failed') {
          resolved = true;
          clearTimeout(timeout);
          pc.close();
          resolve(null);
        }
      };

      // Create data channel to trigger connection
      const dataChannel = pc.createDataChannel('test', {
        ordered: true
      });

      dataChannel.onopen = () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve(pc);
        }
      };

      dataChannel.onerror = () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          pc.close();
          resolve(null);
        }
      };

      // Start connection process
      if (isInitiator) {
        pc.createOffer().then(offer => {
          pc.setLocalDescription(offer);
          // In real implementation, send offer to peer via signaling
        });
      }
    });
  }

  /**
   * Get current attempt status
   */
  getCurrentAttempt(): FallbackAttempt | null {
    return this.currentAttempt;
  }

  /**
   * Get attempt history
   */
  getAttemptHistory(): FallbackAttempt[] {
    return [...this.attemptHistory];
  }

  /**
   * Get available strategies
   */
  getStrategies(): FallbackStrategy[] {
    return [...this.strategies];
  }

  /**
   * Clear attempt history
   */
  clearHistory(): void {
    this.attemptHistory = [];
    this.emit('history-cleared');
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Emit events
   */
  private emit(type: string, detail?: any): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
