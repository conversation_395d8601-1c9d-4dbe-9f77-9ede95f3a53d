/**
 * Advanced Network Analysis for P2P Connections
 * 
 * Provides comprehensive network connectivity testing, NAT type detection,
 * firewall analysis, and connection quality assessment.
 */

export interface NetworkCapabilities {
  supportsWebRTC: boolean;
  supportsDataChannels: boolean;
  supportsGetUserMedia: boolean;
  supportsWebSockets: boolean;
  browserInfo: {
    name: string;
    version: string;
    engine: string;
  };
}

export interface ConnectionTest {
  protocol: 'udp' | 'tcp' | 'websocket';
  host: string;
  port: number;
  timeout: number;
}

export interface ConnectionTestResult {
  protocol: 'udp' | 'tcp' | 'websocket';
  host: string;
  port: number;
  success: boolean;
  latency: number;
  error?: string;
}

export interface NATAnalysis {
  type: 'none' | 'full-cone' | 'restricted' | 'port-restricted' | 'symmetric' | 'unknown';
  confidence: number; // 0-1
  externalIP?: string;
  mappedPorts: number[];
  consistentMapping: boolean;
  supportsHairpinning: boolean;
}

export interface FirewallAnalysis {
  blocksUDP: boolean;
  blocksTCP: boolean;
  blocksWebSockets: boolean;
  allowedPorts: number[];
  blockedPorts: number[];
  hasDeepPacketInspection: boolean;
  corporateFirewall: boolean;
}

export interface BandwidthMeasurement {
  downloadSpeed: number; // Mbps
  uploadSpeed: number; // Mbps
  latency: number; // ms
  jitter: number; // ms
  packetLoss: number; // percentage
  timestamp: number;
}

export interface NetworkQualityAssessment {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  p2pSuitability: 'ideal' | 'suitable' | 'challenging' | 'unsuitable';
  recommendedStrategy: 'direct' | 'stun' | 'turn' | 'relay';
  issues: string[];
  recommendations: string[];
}

export class NetworkAnalyzer extends EventTarget {
  private capabilities: NetworkCapabilities | null = null;
  private natAnalysis: NATAnalysis | null = null;
  private firewallAnalysis: FirewallAnalysis | null = null;
  private bandwidthHistory: BandwidthMeasurement[] = [];

  constructor() {
    super();
  }

  /**
   * Perform comprehensive network analysis
   */
  async analyzeNetwork(): Promise<{
    capabilities: NetworkCapabilities;
    nat: NATAnalysis;
    firewall: FirewallAnalysis;
    bandwidth: BandwidthMeasurement;
    quality: NetworkQualityAssessment;
  }> {
    console.log('Starting comprehensive network analysis...');

    try {
      // Run all analyses in parallel where possible
      const [capabilities, nat, firewall, bandwidth] = await Promise.all([
        this.analyzeCapabilities(),
        this.analyzeNAT(),
        this.analyzeFirewall(),
        this.measureBandwidth()
      ]);

      const quality = this.assessNetworkQuality(capabilities, nat, firewall, bandwidth);

      this.emit('analysis-complete', {
        capabilities,
        nat,
        firewall,
        bandwidth,
        quality
      });

      return { capabilities, nat, firewall, bandwidth, quality };
    } catch (error) {
      console.error('Network analysis failed:', error);
      throw error;
    }
  }

  /**
   * Analyze browser and WebRTC capabilities
   */
  async analyzeCapabilities(): Promise<NetworkCapabilities> {
    const capabilities: NetworkCapabilities = {
      supportsWebRTC: false,
      supportsDataChannels: false,
      supportsGetUserMedia: false,
      supportsWebSockets: false,
      browserInfo: this.getBrowserInfo()
    };

    // Test WebRTC support
    try {
      const pc = new RTCPeerConnection();
      capabilities.supportsWebRTC = true;
      
      // Test data channels
      try {
        const dc = pc.createDataChannel('test');
        capabilities.supportsDataChannels = true;
        dc.close();
      } catch (error) {
        console.warn('Data channels not supported:', error);
      }
      
      pc.close();
    } catch (error) {
      console.warn('WebRTC not supported:', error);
    }

    // Test getUserMedia
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        capabilities.supportsGetUserMedia = true;
      }
    } catch (error) {
      console.warn('getUserMedia not supported:', error);
    }

    // Test WebSocket support
    try {
      capabilities.supportsWebSockets = typeof WebSocket !== 'undefined';
    } catch (error) {
      console.warn('WebSockets not supported:', error);
    }

    this.capabilities = capabilities;
    return capabilities;
  }

  /**
   * Perform detailed NAT type detection
   */
  async analyzeNAT(): Promise<NATAnalysis> {
    const analysis: NATAnalysis = {
      type: 'unknown',
      confidence: 0,
      mappedPorts: [],
      consistentMapping: false,
      supportsHairpinning: false
    };

    try {
      // Use multiple STUN servers for comprehensive analysis
      const stunServers = [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302',
        'stun:stun.services.mozilla.com:3478',
        'stun:stun.cloudflare.com:3478'
      ];

      const results = await Promise.allSettled(
        stunServers.map(server => this.testSTUNServer(server))
      );

      const successfulResults = results
        .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        .map(result => result.value)
        .filter(result => result !== null);

      if (successfulResults.length === 0) {
        analysis.type = 'unknown';
        analysis.confidence = 0;
        return analysis;
      }

      // Analyze results to determine NAT type
      analysis.externalIP = successfulResults[0]?.externalIP;
      analysis.mappedPorts = successfulResults.map(r => r.mappedPort).filter(p => p);
      analysis.consistentMapping = this.checkConsistentMapping(successfulResults);
      
      // Determine NAT type based on results
      if (successfulResults.length === 0) {
        analysis.type = 'symmetric'; // Assume worst case
        analysis.confidence = 0.3;
      } else if (analysis.consistentMapping && analysis.mappedPorts.length > 0) {
        if (this.hasPublicIP(analysis.externalIP)) {
          analysis.type = 'none';
          analysis.confidence = 0.9;
        } else {
          analysis.type = 'full-cone';
          analysis.confidence = 0.8;
        }
      } else {
        analysis.type = 'symmetric';
        analysis.confidence = 0.7;
      }

      // Test hairpinning if possible
      analysis.supportsHairpinning = await this.testHairpinning();

    } catch (error) {
      console.error('NAT analysis failed:', error);
      analysis.type = 'unknown';
      analysis.confidence = 0;
    }

    this.natAnalysis = analysis;
    return analysis;
  }

  /**
   * Test STUN server connectivity
   */
  private async testSTUNServer(stunUrl: string): Promise<{
    externalIP: string;
    mappedPort: number;
    localPort: number;
  } | null> {
    return new Promise((resolve) => {
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: stunUrl }]
      });

      const timeout = setTimeout(() => {
        pc.close();
        resolve(null);
      }, 10000);

      pc.onicecandidate = (event) => {
        if (event.candidate && event.candidate.type === 'srflx') {
          clearTimeout(timeout);
          pc.close();
          
          const parts = event.candidate.candidate.split(' ');
          const externalIP = parts[4];
          const mappedPort = parseInt(parts[5]);
          const localPort = parseInt(parts[1]);
          
          resolve({ externalIP, mappedPort, localPort });
        } else if (!event.candidate) {
          // ICE gathering complete
          clearTimeout(timeout);
          pc.close();
          resolve(null);
        }
      };

      // Create data channel to trigger ICE gathering
      pc.createDataChannel('test');
      pc.createOffer().then(offer => pc.setLocalDescription(offer));
    });
  }

  /**
   * Check if port mapping is consistent across STUN servers
   */
  private checkConsistentMapping(results: any[]): boolean {
    if (results.length < 2) return true;
    
    const firstPort = results[0].mappedPort;
    return results.every(result => result.mappedPort === firstPort);
  }

  /**
   * Check if IP address is public
   */
  private hasPublicIP(ip?: string): boolean {
    if (!ip) return false;
    
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./, // Link-local
      /^::1$/, // IPv6 loopback
      /^fe80:/, // IPv6 link-local
    ];
    
    return !privateRanges.some(range => range.test(ip));
  }

  /**
   * Test NAT hairpinning support
   */
  private async testHairpinning(): Promise<boolean> {
    // This is a simplified test - in practice, you'd need a more sophisticated approach
    return false; // Conservative assumption
  }

  /**
   * Analyze firewall restrictions
   */
  async analyzeFirewall(): Promise<FirewallAnalysis> {
    const analysis: FirewallAnalysis = {
      blocksUDP: false,
      blocksTCP: false,
      blocksWebSockets: false,
      allowedPorts: [],
      blockedPorts: [],
      hasDeepPacketInspection: false,
      corporateFirewall: false
    };

    try {
      // Test common ports
      const testPorts = [80, 443, 3478, 5349, 8080, 8443];
      const connectionTests: ConnectionTest[] = [
        ...testPorts.map(port => ({
          protocol: 'websocket' as const,
          host: 'echo.websocket.org',
          port,
          timeout: 5000
        }))
      ];

      const results = await Promise.allSettled(
        connectionTests.map(test => this.testConnection(test))
      );

      const successfulResults = results
        .filter((result): result is PromiseFulfilledResult<ConnectionTestResult> => 
          result.status === 'fulfilled')
        .map(result => result.value);

      // Analyze results
      analysis.allowedPorts = successfulResults
        .filter(result => result.success)
        .map(result => result.port);
      
      analysis.blockedPorts = successfulResults
        .filter(result => !result.success)
        .map(result => result.port);

      // Detect corporate firewall patterns
      analysis.corporateFirewall = this.detectCorporateFirewall(successfulResults);
      analysis.blocksWebSockets = !successfulResults.some(r => r.success && r.protocol === 'websocket');

      // Simplified DPI detection
      analysis.hasDeepPacketInspection = analysis.corporateFirewall && 
        analysis.blockedPorts.length > analysis.allowedPorts.length;

    } catch (error) {
      console.error('Firewall analysis failed:', error);
    }

    this.firewallAnalysis = analysis;
    return analysis;
  }

  /**
   * Test connection to a specific endpoint
   */
  private async testConnection(test: ConnectionTest): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      if (test.protocol === 'websocket') {
        return await this.testWebSocketConnection(test, startTime);
      }
      
      // For UDP/TCP, we'd need a different approach
      throw new Error(`Protocol ${test.protocol} not implemented`);
    } catch (error) {
      return {
        protocol: test.protocol,
        host: test.host,
        port: test.port,
        success: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test WebSocket connection
   */
  private async testWebSocketConnection(test: ConnectionTest, startTime: number): Promise<ConnectionTestResult> {
    return new Promise((resolve) => {
      const ws = new WebSocket(`wss://${test.host}:${test.port}`);
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          protocol: test.protocol,
          host: test.host,
          port: test.port,
          success: false,
          latency: Date.now() - startTime,
          error: 'Connection timeout'
        });
      }, test.timeout);

      ws.onopen = () => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          protocol: test.protocol,
          host: test.host,
          port: test.port,
          success: true,
          latency: Date.now() - startTime
        });
      };

      ws.onerror = () => {
        clearTimeout(timeout);
        resolve({
          protocol: test.protocol,
          host: test.host,
          port: test.port,
          success: false,
          latency: Date.now() - startTime,
          error: 'Connection failed'
        });
      };
    });
  }

  /**
   * Detect corporate firewall patterns
   */
  private detectCorporateFirewall(results: ConnectionTestResult[]): boolean {
    // Look for patterns typical of corporate firewalls
    const allowedPorts = results.filter(r => r.success).map(r => r.port);
    const blockedPorts = results.filter(r => !r.success).map(r => r.port);
    
    // Corporate firewalls typically allow 80, 443 but block others
    const allowsStandardPorts = allowedPorts.includes(80) || allowedPorts.includes(443);
    const blocksNonStandardPorts = blockedPorts.length > allowedPorts.length;
    
    return allowsStandardPorts && blocksNonStandardPorts;
  }

  /**
   * Measure network bandwidth and quality
   */
  async measureBandwidth(): Promise<BandwidthMeasurement> {
    // This is a simplified implementation
    // In practice, you'd use more sophisticated bandwidth testing
    const measurement: BandwidthMeasurement = {
      downloadSpeed: 0,
      uploadSpeed: 0,
      latency: 0,
      jitter: 0,
      packetLoss: 0,
      timestamp: Date.now()
    };

    try {
      // Measure latency using a simple ping-like test
      const latencyTests = await Promise.all([
        this.measureLatency('https://www.google.com/favicon.ico'),
        this.measureLatency('https://www.cloudflare.com/favicon.ico'),
        this.measureLatency('https://www.mozilla.org/favicon.ico')
      ]);

      const validLatencies = latencyTests.filter(l => l > 0);
      if (validLatencies.length > 0) {
        measurement.latency = validLatencies.reduce((a, b) => a + b) / validLatencies.length;
        measurement.jitter = this.calculateJitter(validLatencies);
      }

      // Simplified bandwidth estimation based on latency
      if (measurement.latency < 50) {
        measurement.downloadSpeed = 100; // Assume good connection
        measurement.uploadSpeed = 50;
      } else if (measurement.latency < 200) {
        measurement.downloadSpeed = 25;
        measurement.uploadSpeed = 10;
      } else {
        measurement.downloadSpeed = 5;
        measurement.uploadSpeed = 1;
      }

    } catch (error) {
      console.error('Bandwidth measurement failed:', error);
    }

    this.bandwidthHistory.push(measurement);
    if (this.bandwidthHistory.length > 10) {
      this.bandwidthHistory = this.bandwidthHistory.slice(-10);
    }

    return measurement;
  }

  /**
   * Measure latency to a specific endpoint
   */
  private async measureLatency(url: string): Promise<number> {
    const startTime = Date.now();
    
    try {
      await fetch(url, { 
        method: 'HEAD', 
        mode: 'no-cors',
        cache: 'no-cache'
      });
      return Date.now() - startTime;
    } catch (error) {
      return -1; // Failed
    }
  }

  /**
   * Calculate jitter from latency measurements
   */
  private calculateJitter(latencies: number[]): number {
    if (latencies.length < 2) return 0;
    
    const differences = [];
    for (let i = 1; i < latencies.length; i++) {
      differences.push(Math.abs(latencies[i] - latencies[i - 1]));
    }
    
    return differences.reduce((a, b) => a + b) / differences.length;
  }

  /**
   * Assess overall network quality for P2P communications
   */
  private assessNetworkQuality(
    capabilities: NetworkCapabilities,
    nat: NATAnalysis,
    firewall: FirewallAnalysis,
    bandwidth: BandwidthMeasurement
  ): NetworkQualityAssessment {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check capabilities
    if (!capabilities.supportsWebRTC) {
      issues.push('WebRTC not supported');
      score -= 50;
    }
    if (!capabilities.supportsDataChannels) {
      issues.push('Data channels not supported');
      score -= 20;
    }

    // Check NAT
    if (nat.type === 'symmetric') {
      issues.push('Symmetric NAT detected');
      recommendations.push('TURN server required for reliable connections');
      score -= 30;
    } else if (nat.type === 'unknown') {
      issues.push('NAT type could not be determined');
      score -= 20;
    }

    // Check firewall
    if (firewall.corporateFirewall) {
      issues.push('Corporate firewall detected');
      recommendations.push('May need TURN server on port 443');
      score -= 25;
    }
    if (firewall.blocksWebSockets) {
      issues.push('WebSocket connections blocked');
      score -= 15;
    }

    // Check bandwidth
    if (bandwidth.latency > 500) {
      issues.push('High latency detected');
      score -= 20;
    }
    if (bandwidth.downloadSpeed < 1) {
      issues.push('Very low bandwidth');
      score -= 25;
    }

    // Determine overall quality
    let overall: NetworkQualityAssessment['overall'];
    let p2pSuitability: NetworkQualityAssessment['p2pSuitability'];
    let recommendedStrategy: NetworkQualityAssessment['recommendedStrategy'];

    if (score >= 80) {
      overall = 'excellent';
      p2pSuitability = 'ideal';
      recommendedStrategy = 'direct';
    } else if (score >= 60) {
      overall = 'good';
      p2pSuitability = 'suitable';
      recommendedStrategy = nat.type === 'symmetric' ? 'turn' : 'stun';
    } else if (score >= 40) {
      overall = 'fair';
      p2pSuitability = 'challenging';
      recommendedStrategy = 'turn';
    } else {
      overall = 'poor';
      p2pSuitability = 'unsuitable';
      recommendedStrategy = 'relay';
    }

    return {
      overall,
      p2pSuitability,
      recommendedStrategy,
      issues,
      recommendations
    };
  }

  /**
   * Get browser information
   */
  private getBrowserInfo(): NetworkCapabilities['browserInfo'] {
    const userAgent = navigator.userAgent;
    
    let name = 'Unknown';
    let version = 'Unknown';
    let engine = 'Unknown';

    if (userAgent.includes('Chrome')) {
      name = 'Chrome';
      engine = 'Blink';
      const match = userAgent.match(/Chrome\/(\d+)/);
      if (match) version = match[1];
    } else if (userAgent.includes('Firefox')) {
      name = 'Firefox';
      engine = 'Gecko';
      const match = userAgent.match(/Firefox\/(\d+)/);
      if (match) version = match[1];
    } else if (userAgent.includes('Safari')) {
      name = 'Safari';
      engine = 'WebKit';
      const match = userAgent.match(/Version\/(\d+)/);
      if (match) version = match[1];
    } else if (userAgent.includes('Edge')) {
      name = 'Edge';
      engine = 'Blink';
      const match = userAgent.match(/Edge\/(\d+)/);
      if (match) version = match[1];
    }

    return { name, version, engine };
  }

  /**
   * Get current network analysis results
   */
  getAnalysisResults() {
    return {
      capabilities: this.capabilities,
      nat: this.natAnalysis,
      firewall: this.firewallAnalysis,
      bandwidthHistory: this.bandwidthHistory
    };
  }

  /**
   * Emit events
   */
  private emit(type: string, detail: any): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
