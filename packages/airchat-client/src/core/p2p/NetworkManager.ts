export interface NetworkStats {
  isOnline: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  effectiveType: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  downlink: number; // Mbps
  rtt: number; // milliseconds
  saveData: boolean;
}

export interface BandwidthMeasurement {
  timestamp: number;
  downloadSpeed: number; // bytes per second
  uploadSpeed: number; // bytes per second
  latency: number; // milliseconds
  packetLoss: number; // percentage
}

export interface NetworkQuality {
  score: number; // 0-100
  category: 'poor' | 'fair' | 'good' | 'excellent';
  recommendation: string;
}

export interface NetworkManagerEvents {
  'network-change': { stats: NetworkStats };
  'bandwidth-update': { measurement: BandwidthMeasurement };
  'quality-change': { quality: NetworkQuality };
  'connection-lost': void;
  'connection-restored': void;
}

export class NetworkManager extends EventTarget {
  private stats: NetworkStats;
  private measurements: BandwidthMeasurement[] = [];
  private measurementTimer: number | null = null;
  private qualityTimer: number | null = null;
  private isDestroyed = false;
  private lastOnlineState = true;

  constructor() {
    super();
    
    this.stats = this.getCurrentNetworkStats();
    this.setupEventListeners();
    this.startMonitoring();
  }

  private getCurrentNetworkStats(): NetworkStats {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    return {
      isOnline: navigator.onLine,
      connectionType: connection?.type || 'unknown',
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      rtt: connection?.rtt || 0,
      saveData: connection?.saveData || false
    };
  }

  private setupEventListeners(): void {
    // Online/offline events
    window.addEventListener('online', this.handleOnlineChange);
    window.addEventListener('offline', this.handleOnlineChange);

    // Connection change events
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', this.handleConnectionChange);
    }

    // Visibility change to pause/resume monitoring
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  }

  private handleOnlineChange = (): void => {
    const wasOnline = this.lastOnlineState;
    const isOnline = navigator.onLine;
    
    this.stats.isOnline = isOnline;
    this.lastOnlineState = isOnline;
    
    this.emit('network-change', { stats: this.stats });
    
    if (!wasOnline && isOnline) {
      this.emit('connection-restored', undefined);
    } else if (wasOnline && !isOnline) {
      this.emit('connection-lost', undefined);
    }
  };

  private handleConnectionChange = (): void => {
    this.stats = this.getCurrentNetworkStats();
    this.emit('network-change', { stats: this.stats });
  };

  private handleVisibilityChange = (): void => {
    if (document.hidden) {
      this.pauseMonitoring();
    } else {
      this.resumeMonitoring();
    }
  };

  private startMonitoring(): void {
    // Measure bandwidth every 30 seconds
    this.measurementTimer = window.setInterval(() => {
      this.measureBandwidth();
    }, 30000);

    // Calculate quality every 10 seconds
    this.qualityTimer = window.setInterval(() => {
      this.calculateNetworkQuality();
    }, 10000);

    // Initial measurements
    this.measureBandwidth();
    this.calculateNetworkQuality();
  }

  private pauseMonitoring(): void {
    if (this.measurementTimer) {
      clearInterval(this.measurementTimer);
      this.measurementTimer = null;
    }
    if (this.qualityTimer) {
      clearInterval(this.qualityTimer);
      this.qualityTimer = null;
    }
  }

  private resumeMonitoring(): void {
    if (!this.measurementTimer && !this.isDestroyed) {
      this.startMonitoring();
    }
  }

  private async measureBandwidth(): Promise<void> {
    if (!this.stats.isOnline || this.isDestroyed) return;

    try {
      const measurement = await this.performBandwidthTest();
      this.measurements.push(measurement);
      
      // Keep only last 20 measurements (10 minutes of data)
      if (this.measurements.length > 20) {
        this.measurements = this.measurements.slice(-20);
      }
      
      this.emit('bandwidth-update', { measurement });
    } catch (error) {
      console.error('Bandwidth measurement failed:', error);
    }
  }

  private async performBandwidthTest(): Promise<BandwidthMeasurement> {
    const startTime = performance.now();
    
    // Download test - fetch a small image
    const downloadStart = performance.now();
    try {
      const response = await fetch('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', {
        cache: 'no-cache'
      });
      await response.blob();
    } catch (error) {
      // Fallback for download test
    }
    const downloadTime = performance.now() - downloadStart;
    
    // Simple latency test using performance.now()
    const latencyStart = performance.now();
    await new Promise(resolve => setTimeout(resolve, 1));
    const latency = performance.now() - latencyStart;

    // Estimate speeds based on connection info and timing
    const connection = (navigator as any).connection;
    const estimatedDownload = connection?.downlink ? connection.downlink * 125000 : 1000000; // Convert Mbps to bytes/sec
    const estimatedUpload = estimatedDownload * 0.8; // Assume upload is 80% of download

    return {
      timestamp: Date.now(),
      downloadSpeed: estimatedDownload,
      uploadSpeed: estimatedUpload,
      latency: Math.max(latency, this.stats.rtt || 50),
      packetLoss: 0 // Would need more sophisticated testing for real packet loss
    };
  }

  private calculateNetworkQuality(): void {
    if (this.measurements.length === 0) return;

    const recent = this.measurements.slice(-5); // Last 5 measurements
    const avgDownload = recent.reduce((sum, m) => sum + m.downloadSpeed, 0) / recent.length;
    const avgLatency = recent.reduce((sum, m) => sum + m.latency, 0) / recent.length;
    const avgPacketLoss = recent.reduce((sum, m) => sum + m.packetLoss, 0) / recent.length;

    // Calculate quality score (0-100)
    let score = 100;
    
    // Penalize based on download speed
    if (avgDownload < 100000) score -= 40; // < 100 KB/s
    else if (avgDownload < 500000) score -= 20; // < 500 KB/s
    else if (avgDownload < 1000000) score -= 10; // < 1 MB/s
    
    // Penalize based on latency
    if (avgLatency > 500) score -= 30;
    else if (avgLatency > 200) score -= 15;
    else if (avgLatency > 100) score -= 5;
    
    // Penalize based on packet loss
    score -= avgPacketLoss * 2;
    
    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, score));
    
    let category: NetworkQuality['category'];
    let recommendation: string;
    
    if (score >= 80) {
      category = 'excellent';
      recommendation = 'Network quality is excellent. All features should work smoothly.';
    } else if (score >= 60) {
      category = 'good';
      recommendation = 'Network quality is good. Most features should work well.';
    } else if (score >= 40) {
      category = 'fair';
      recommendation = 'Network quality is fair. Some features may be slower.';
    } else {
      category = 'poor';
      recommendation = 'Network quality is poor. Consider switching to a better connection.';
    }

    const quality: NetworkQuality = { score, category, recommendation };
    this.emit('quality-change', { quality });
  }

  getNetworkStats(): NetworkStats {
    return { ...this.stats };
  }

  getLatestMeasurement(): BandwidthMeasurement | null {
    return this.measurements.length > 0 ? this.measurements[this.measurements.length - 1] : null;
  }

  getMeasurementHistory(): BandwidthMeasurement[] {
    return [...this.measurements];
  }

  getAverageStats(minutes: number = 5): {
    downloadSpeed: number;
    uploadSpeed: number;
    latency: number;
    packetLoss: number;
  } | null {
    const cutoff = Date.now() - minutes * 60 * 1000;
    const recentMeasurements = this.measurements.filter(m => m.timestamp >= cutoff);
    
    if (recentMeasurements.length === 0) return null;
    
    return {
      downloadSpeed: recentMeasurements.reduce((sum, m) => sum + m.downloadSpeed, 0) / recentMeasurements.length,
      uploadSpeed: recentMeasurements.reduce((sum, m) => sum + m.uploadSpeed, 0) / recentMeasurements.length,
      latency: recentMeasurements.reduce((sum, m) => sum + m.latency, 0) / recentMeasurements.length,
      packetLoss: recentMeasurements.reduce((sum, m) => sum + m.packetLoss, 0) / recentMeasurements.length
    };
  }

  getCurrentQuality(): NetworkQuality {
    const recent = this.measurements.slice(-3);
    if (recent.length === 0) {
      return {
        score: 50,
        category: 'fair',
        recommendation: 'Measuring network quality...'
      };
    }

    const avgDownload = recent.reduce((sum, m) => sum + m.downloadSpeed, 0) / recent.length;
    const avgLatency = recent.reduce((sum, m) => sum + m.latency, 0) / recent.length;

    let score = 100;
    if (avgDownload < 100000) score -= 40;
    else if (avgDownload < 500000) score -= 20;
    else if (avgDownload < 1000000) score -= 10;
    
    if (avgLatency > 500) score -= 30;
    else if (avgLatency > 200) score -= 15;
    else if (avgLatency > 100) score -= 5;

    score = Math.max(0, Math.min(100, score));

    let category: NetworkQuality['category'];
    let recommendation: string;
    
    if (score >= 80) {
      category = 'excellent';
      recommendation = 'Network quality is excellent.';
    } else if (score >= 60) {
      category = 'good';
      recommendation = 'Network quality is good.';
    } else if (score >= 40) {
      category = 'fair';
      recommendation = 'Network quality is fair.';
    } else {
      category = 'poor';
      recommendation = 'Network quality is poor.';
    }

    return { score, category, recommendation };
  }

  isHighQualityConnection(): boolean {
    const quality = this.getCurrentQuality();
    return quality.score >= 60;
  }

  shouldReduceQuality(): boolean {
    const quality = this.getCurrentQuality();
    return quality.score < 40 || this.stats.saveData;
  }

  destroy(): void {
    this.isDestroyed = true;
    this.pauseMonitoring();
    
    window.removeEventListener('online', this.handleOnlineChange);
    window.removeEventListener('offline', this.handleOnlineChange);
    
    const connection = (navigator as any).connection;
    if (connection) {
      connection.removeEventListener('change', this.handleConnectionChange);
    }
    
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  }

  private emit<K extends keyof NetworkManagerEvents>(
    type: K,
    detail: NetworkManagerEvents[K]
  ): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
