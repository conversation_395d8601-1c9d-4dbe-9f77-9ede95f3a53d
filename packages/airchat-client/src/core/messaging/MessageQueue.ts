import type { EncryptedMessage } from '@airchat/shared';
import type { SimpleMessage } from './SecureMessaging';

export interface QueuedMessage {
  id: string;
  message: SimpleMessage | EncryptedMessage;
  to: string;
  attempts: number;
  maxAttempts: number;
  nextRetry: number;
  priority: 'low' | 'normal' | 'high';
  createdAt: number;
  lastAttempt?: number;
  error?: string;
}

export interface MessageQueueConfig {
  maxAttempts?: number;
  baseRetryDelay?: number;
  maxRetryDelay?: number;
  persistenceKey?: string;
  batchSize?: number;
  processingInterval?: number;
}

export interface MessageQueueEvents {
  'message-queued': { messageId: string; to: string };
  'message-sent': { messageId: string; to: string };
  'message-failed': { messageId: string; to: string; error: string };
  'queue-processed': { sent: number; failed: number; remaining: number };
}

export class MessageQueue extends EventTarget {
  private config: Required<MessageQueueConfig>;
  private queue = new Map<string, QueuedMessage>();
  private processing = false;
  private processingTimer: number | null = null;
  private sendCallback: (
    message: SimpleMessage | EncryptedMessage,
    to: string
  ) => Promise<void>;

  constructor(
    sendCallback: (
      message: SimpleMessage | EncryptedMessage,
      to: string
    ) => Promise<void>,
    config: Partial<MessageQueueConfig> = {}
  ) {
    super();

    this.sendCallback = sendCallback;
    this.config = {
      maxAttempts: 5,
      baseRetryDelay: 1000,
      maxRetryDelay: 30000,
      persistenceKey: 'airchat-message-queue',
      batchSize: 10,
      processingInterval: 5000,
      ...config,
    };

    this.loadFromStorage();
    this.startProcessing();
  }

  async enqueue(
    message: SimpleMessage | EncryptedMessage,
    to: string,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<void> {
    const queuedMessage: QueuedMessage = {
      id: message.id,
      message,
      to,
      attempts: 0,
      maxAttempts: this.config.maxAttempts,
      nextRetry: Date.now(),
      priority,
      createdAt: Date.now(),
    };

    this.queue.set(message.id, queuedMessage);
    await this.saveToStorage();

    this.emit('message-queued', { messageId: message.id, to });

    // Try to process immediately if not already processing
    if (!this.processing) {
      this.processQueue();
    }
  }

  async dequeue(messageId: string): Promise<boolean> {
    const removed = this.queue.delete(messageId);
    if (removed) {
      await this.saveToStorage();
    }
    return removed;
  }

  async retry(messageId: string): Promise<void> {
    const queuedMessage = this.queue.get(messageId);
    if (!queuedMessage) return;

    queuedMessage.attempts = 0;
    queuedMessage.nextRetry = Date.now();
    queuedMessage.error = undefined;

    await this.saveToStorage();

    if (!this.processing) {
      this.processQueue();
    }
  }

  async clear(): Promise<void> {
    this.queue.clear();
    await this.saveToStorage();
  }

  async clearFailed(): Promise<void> {
    const failedMessages = Array.from(this.queue.entries()).filter(
      ([_, msg]) => msg.attempts >= msg.maxAttempts
    );

    for (const [id] of failedMessages) {
      this.queue.delete(id);
    }

    await this.saveToStorage();
  }

  getQueuedMessages(): QueuedMessage[] {
    return Array.from(this.queue.values()).sort((a, b) => {
      // Sort by priority first, then by creation time
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityDiff =
        priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      return a.createdAt - b.createdAt;
    });
  }

  getFailedMessages(): QueuedMessage[] {
    return Array.from(this.queue.values()).filter(
      msg => msg.attempts >= msg.maxAttempts
    );
  }

  getPendingMessages(): QueuedMessage[] {
    return Array.from(this.queue.values()).filter(
      msg => msg.attempts < msg.maxAttempts
    );
  }

  getQueueStats() {
    const messages = Array.from(this.queue.values());
    return {
      total: messages.length,
      pending: messages.filter(msg => msg.attempts < msg.maxAttempts).length,
      failed: messages.filter(msg => msg.attempts >= msg.maxAttempts).length,
      highPriority: messages.filter(msg => msg.priority === 'high').length,
      normalPriority: messages.filter(msg => msg.priority === 'normal').length,
      lowPriority: messages.filter(msg => msg.priority === 'low').length,
    };
  }

  private async processQueue(): Promise<void> {
    if (this.processing) return;

    this.processing = true;
    const now = Date.now();
    let sent = 0;
    let failed = 0;

    try {
      // Get messages ready for processing
      const readyMessages = this.getQueuedMessages()
        .filter(msg => msg.attempts < msg.maxAttempts && msg.nextRetry <= now)
        .slice(0, this.config.batchSize);

      for (const queuedMessage of readyMessages) {
        try {
          await this.sendCallback(queuedMessage.message, queuedMessage.to);

          // Success - remove from queue
          this.queue.delete(queuedMessage.id);
          sent++;

          this.emit('message-sent', {
            messageId: queuedMessage.id,
            to: queuedMessage.to,
          });
        } catch (error) {
          // Failure - update retry info
          queuedMessage.attempts++;
          queuedMessage.lastAttempt = now;
          queuedMessage.error =
            error instanceof Error ? error.message : String(error);

          if (queuedMessage.attempts >= queuedMessage.maxAttempts) {
            failed++;
            this.emit('message-failed', {
              messageId: queuedMessage.id,
              to: queuedMessage.to,
              error: queuedMessage.error,
            });
          } else {
            // Calculate next retry time with exponential backoff
            const delay = Math.min(
              this.config.baseRetryDelay *
                Math.pow(2, queuedMessage.attempts - 1),
              this.config.maxRetryDelay
            );
            queuedMessage.nextRetry = now + delay;
          }
        }
      }

      await this.saveToStorage();

      this.emit('queue-processed', {
        sent,
        failed,
        remaining: this.queue.size,
      });
    } catch (error) {
      console.error('Error processing message queue:', error);
    } finally {
      this.processing = false;
    }
  }

  private startProcessing(): void {
    this.stopProcessing();

    this.processingTimer = window.setInterval(() => {
      this.processQueue();
    }, this.config.processingInterval);
  }

  private stopProcessing(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
      this.processingTimer = null;
    }
  }

  private async saveToStorage(): Promise<void> {
    try {
      const data = Array.from(this.queue.entries());
      localStorage.setItem(this.config.persistenceKey, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save message queue to storage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem(this.config.persistenceKey);
      if (data) {
        const entries: [string, QueuedMessage][] = JSON.parse(data);
        this.queue = new Map(entries);

        // Clean up old messages (older than 24 hours)
        const cutoff = Date.now() - 24 * 60 * 60 * 1000;
        for (const [id, message] of this.queue.entries()) {
          if (message.createdAt < cutoff) {
            this.queue.delete(id);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load message queue from storage:', error);
      this.queue.clear();
    }
  }

  destroy(): void {
    this.stopProcessing();
    this.queue.clear();
  }

  private emit<K extends keyof MessageQueueEvents>(
    type: K,
    detail: MessageQueueEvents[K]
  ): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
