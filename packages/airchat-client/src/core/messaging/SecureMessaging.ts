import { EncryptedMessage } from '@airchat/shared';
import { E2EEncryption, DeviceIdentity } from '../crypto/E2EEncryption';
import { KeyManager, ContactKey } from '../crypto/KeyManager';
import { MessageQueue } from './MessageQueue';
import { P2PConnectionManager } from '../p2p/P2PConnectionManager';

// Simple message interface for our implementation
export interface SimpleMessage {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: number;
  type?: string;
}

export interface SecureMessage extends SimpleMessage {
  encrypted: boolean;
  keyFingerprint?: string;
  verified?: boolean;
}

export interface MessageDeliveryStatus {
  messageId: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: number;
  error?: string;
}

export interface SecureMessagingConfig {
  autoEncrypt: boolean;
  requireVerification: boolean;
  keyRotationInterval: number;
  maxRetryAttempts: number;
}

export class SecureMessaging extends EventTarget {
  private encryption: E2EEncryption;
  private keyManager: KeyManager;
  private messageQueue: MessageQueue;
  private connectionManager: P2PConnectionManager;
  private config: SecureMessagingConfig;
  private deliveryStatus: Map<string, MessageDeliveryStatus> = new Map();

  constructor(
    connectionManager: P2PConnectionManager,
    config: Partial<SecureMessagingConfig> = {}
  ) {
    super();

    this.connectionManager = connectionManager;
    this.encryption = new E2EEncryption();
    this.keyManager = new KeyManager();

    this.config = {
      autoEncrypt: true,
      requireVerification: false,
      keyRotationInterval: 24 * 60 * 60 * 1000, // 24 hours
      maxRetryAttempts: 3,
      ...config,
    };

    this.messageQueue = new MessageQueue(this.sendMessageDirectly.bind(this), {
      maxAttempts: this.config.maxRetryAttempts,
    });

    this.setupEventHandlers();
  }

  /**
   * Initialize the secure messaging system
   */
  async initialize(): Promise<DeviceIdentity> {
    await this.keyManager.initialize();

    // Try to load existing device identity
    let identity = this.keyManager.getDeviceIdentity();

    if (!identity) {
      // Generate new identity if none exists
      identity = await this.encryption.initialize();
      await this.keyManager.storeDeviceIdentity(identity);
    } else {
      // Initialize encryption with existing identity
      await this.encryption.initialize();
    }

    this.dispatchEvent(
      new CustomEvent('secure-messaging-ready', {
        detail: { deviceId: identity.deviceId },
      })
    );

    return identity;
  }

  /**
   * Send a secure message to a contact
   */
  async sendMessage(
    to: string,
    content: string,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<string> {
    try {
      const contactKey = this.keyManager.getContactKey(to);
      if (!contactKey) {
        throw new Error(`No encryption key found for contact ${to}`);
      }

      if (this.config.requireVerification && !contactKey.verified) {
        throw new Error('Contact key not verified - message cannot be sent');
      }

      let messageToSend: SimpleMessage | EncryptedMessage;

      if (this.config.autoEncrypt) {
        // Encrypt the message
        const encryptedMessage = await this.encryption.encryptMessage(
          to,
          content
        );
        messageToSend = encryptedMessage;

        this.dispatchEvent(
          new CustomEvent('message-encrypted', {
            detail: { messageId: encryptedMessage.id, to },
          })
        );
      } else {
        // Send as plain text
        messageToSend = {
          id: crypto.randomUUID(),
          from: this.keyManager.getDeviceIdentity()?.deviceId || 'unknown',
          to,
          content,
          timestamp: Date.now(),
          type: 'text',
        };
      }

      // Add to delivery tracking
      this.deliveryStatus.set(messageToSend.id, {
        messageId: messageToSend.id,
        status: 'sending',
        timestamp: Date.now(),
      });

      // Queue for delivery
      await this.messageQueue.enqueue(messageToSend, to, priority);

      this.dispatchEvent(
        new CustomEvent('message-queued', {
          detail: {
            messageId: messageToSend.id,
            to,
            encrypted: this.config.autoEncrypt,
          },
        })
      );

      return messageToSend.id;
    } catch (error) {
      console.error('Failed to send message:', error);

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.dispatchEvent(
        new CustomEvent('message-send-error', {
          detail: { to, error: errorMessage },
        })
      );

      throw error;
    }
  }

  /**
   * Handle received message (encrypted or plain)
   */
  async handleReceivedMessage(
    message: SimpleMessage | EncryptedMessage
  ): Promise<SecureMessage> {
    try {
      let decryptedContent: string;
      let isEncrypted = false;
      let keyFingerprint: string | undefined;
      let verified = false;

      // Check if message is encrypted
      if ('encryptedContent' in message) {
        const encryptedMessage = message as EncryptedMessage;

        // Verify sender has a known key
        const contactKey = this.keyManager.getContactKey(encryptedMessage.from);
        if (!contactKey) {
          throw new Error(
            `No encryption key found for sender ${encryptedMessage.from}`
          );
        }

        // Decrypt the message
        decryptedContent =
          await this.encryption.decryptMessage(encryptedMessage);
        isEncrypted = true;
        keyFingerprint = encryptedMessage.keyFingerprint;
        verified = contactKey.verified;

        this.dispatchEvent(
          new CustomEvent('message-decrypted', {
            detail: {
              messageId: encryptedMessage.id,
              from: encryptedMessage.from,
            },
          })
        );
      } else {
        // Plain text message
        const plainMessage = message as SimpleMessage;
        decryptedContent = plainMessage.content;
      }

      const secureMessage: SecureMessage = {
        id: message.id,
        from: message.from,
        to: message.to,
        content: decryptedContent,
        timestamp: message.timestamp,
        type: message.type || 'text',
        encrypted: isEncrypted,
        keyFingerprint,
        verified,
      };

      // Send delivery confirmation
      await this.sendDeliveryConfirmation(message.id, message.from);

      this.dispatchEvent(
        new CustomEvent('message-received', {
          detail: { message: secureMessage },
        })
      );

      return secureMessage;
    } catch (error) {
      console.error('Failed to handle received message:', error);

      this.dispatchEvent(
        new CustomEvent('message-receive-error', {
          detail: {
            messageId: message.id,
            from: message.from,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        })
      );

      throw error;
    }
  }

  /**
   * Add a contact and establish encryption
   */
  async addContact(
    deviceId: string,
    publicKey: Uint8Array,
    verified: boolean = false
  ): Promise<void> {
    try {
      // Add contact key to key manager
      await this.keyManager.addContactKey(deviceId, publicKey, verified);

      // Derive shared encryption key
      await this.encryption.deriveSharedKey(deviceId, publicKey);

      this.dispatchEvent(
        new CustomEvent('contact-added', {
          detail: { deviceId, verified },
        })
      );
    } catch (error) {
      console.error('Failed to add contact:', error);
      throw error;
    }
  }

  /**
   * Verify a contact's key
   */
  async verifyContact(deviceId: string): Promise<void> {
    await this.keyManager.verifyContactKey(deviceId);

    this.dispatchEvent(
      new CustomEvent('contact-verified', {
        detail: { deviceId },
      })
    );
  }

  /**
   * Get contact information
   */
  getContact(deviceId: string): ContactKey | null {
    return this.keyManager.getContactKey(deviceId);
  }

  /**
   * Get all contacts
   */
  getAllContacts(): ContactKey[] {
    return this.keyManager.getAllContactKeys();
  }

  /**
   * Get message delivery status
   */
  getDeliveryStatus(messageId: string): MessageDeliveryStatus | null {
    return this.deliveryStatus.get(messageId) || null;
  }

  /**
   * Get device identity for sharing
   */
  getDeviceIdentity(): DeviceIdentity | null {
    return this.keyManager.getDeviceIdentity();
  }

  /**
   * Create encrypted backup of keys
   */
  async createKeyBackup(password: string): Promise<string> {
    const backup = await this.keyManager.createKeyBackup(password);
    return JSON.stringify(backup);
  }

  /**
   * Restore from encrypted backup
   */
  async restoreFromBackup(backupData: string, password: string): Promise<void> {
    const backup = JSON.parse(backupData);
    await this.keyManager.restoreFromBackup(backup, password);

    // Reinitialize encryption with restored identity
    await this.encryption.initialize();
  }

  /**
   * Send message directly via P2P connection
   */
  private async sendMessageDirectly(
    message: SimpleMessage | EncryptedMessage,
    to: string
  ): Promise<void> {
    try {
      await this.connectionManager.sendMessage(to, message);

      // Update delivery status
      const status = this.deliveryStatus.get(message.id);
      if (status) {
        status.status = 'sent';
        status.timestamp = Date.now();
      }

      this.dispatchEvent(
        new CustomEvent('message-sent', {
          detail: { messageId: message.id, to },
        })
      );
    } catch (error) {
      // Update delivery status
      const status = this.deliveryStatus.get(message.id);
      if (status) {
        status.status = 'failed';
        status.error = error instanceof Error ? error.message : 'Unknown error';
        status.timestamp = Date.now();
      }

      throw error;
    }
  }

  /**
   * Send delivery confirmation
   */
  private async sendDeliveryConfirmation(
    messageId: string,
    to: string
  ): Promise<void> {
    try {
      const confirmation = {
        type: 'delivery-confirmation',
        messageId,
        timestamp: Date.now(),
      };

      await this.connectionManager.sendMessage(to, confirmation);
    } catch (error) {
      console.error('Failed to send delivery confirmation:', error);
      // Don't throw - delivery confirmations are best effort
    }
  }

  /**
   * Handle delivery confirmation
   */
  private handleDeliveryConfirmation(confirmation: any): void {
    const status = this.deliveryStatus.get(confirmation.messageId);
    if (status && status.status === 'sent') {
      status.status = 'delivered';
      status.timestamp = Date.now();

      this.dispatchEvent(
        new CustomEvent('message-delivered', {
          detail: { messageId: confirmation.messageId },
        })
      );
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle connection manager events
    this.connectionManager.addEventListener(
      'message-received',
      (event: any) => {
        const { message } = event.detail;

        if (message.type === 'delivery-confirmation') {
          this.handleDeliveryConfirmation(message);
        } else {
          this.handleReceivedMessage(message);
        }
      }
    );

    // Handle encryption events
    this.encryption.addEventListener('key-rotation-needed', (event: any) => {
      const { contactDeviceId } = event.detail;
      this.dispatchEvent(
        new CustomEvent('key-rotation-needed', {
          detail: { contactDeviceId },
        })
      );
    });

    // Handle message queue events
    this.messageQueue.addEventListener('queue-processed', (event: any) => {
      this.dispatchEvent(new CustomEvent('queue-processed', event));
    });
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.encryption.destroy();
    this.keyManager.destroy();
    this.messageQueue.destroy();
    this.deliveryStatus.clear();
  }
}
