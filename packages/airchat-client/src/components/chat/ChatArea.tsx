import React from 'react';
import { useAppStore } from '../../stores/appStore';
import { ContactList } from './ContactList';
import { MessageView } from './MessageView';

export function ChatArea() {
  const { selectedContact, contacts } = useAppStore();

  if (contacts.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-primary">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">👋</div>
          <h2 className="text-xl font-semibold mb-2">Welcome to AirChat!</h2>
          <p className="text-secondary mb-6">
            You don't have any contacts yet. Add your first contact to start messaging.
          </p>
          <button
            className="btn btn-primary"
            onClick={() => useAppStore.getState().setCurrentView('contacts')}
          >
            Add Contact
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex">
      <div className="w-80 border-r border-color">
        <ContactList />
      </div>
      <div className="flex-1">
        {selectedContact ? (
          <MessageView contactId={selectedContact} />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-primary">
            <div className="text-center">
              <div className="text-4xl mb-4">💬</div>
              <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
              <p className="text-secondary">
                Choose a contact from the list to start messaging
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
