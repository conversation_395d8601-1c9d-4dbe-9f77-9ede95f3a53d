import React from 'react';
import { useAppStore } from '../../stores/appStore';
import { formatDistanceToNow } from 'date-fns';

export function ContactList() {
  const { 
    contacts, 
    selectedContact, 
    selectContact, 
    messages, 
    unreadCounts 
  } = useAppStore();

  const getLastMessage = (contactId: string) => {
    const contactMessages = messages.get(contactId) || [];
    return contactMessages[contactMessages.length - 1];
  };

  const getContactInitials = (deviceId: string) => {
    return deviceId.slice(0, 2).toUpperCase();
  };

  const formatLastMessageTime = (timestamp: number) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return '';
    }
  };

  return (
    <div className="h-full flex flex-col bg-secondary">
      {/* Header */}
      <div className="p-4 border-b border-color">
        <h2 className="text-lg font-semibold">Messages</h2>
      </div>

      {/* Contact List */}
      <div className="flex-1 overflow-y-auto">
        {contacts.length === 0 ? (
          <div className="p-4 text-center text-secondary">
            <p>No contacts yet</p>
          </div>
        ) : (
          <div className="divide-y divide-border-color">
            {contacts.map((contact) => {
              const lastMessage = getLastMessage(contact.deviceId);
              const unreadCount = unreadCounts.get(contact.deviceId) || 0;
              const isSelected = selectedContact === contact.deviceId;

              return (
                <button
                  key={contact.deviceId}
                  className={`w-full p-4 text-left transition hover:bg-tertiary ${
                    isSelected ? 'bg-accent text-white' : ''
                  }`}
                  onClick={() => selectContact(contact.deviceId)}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className={`w-12 h-12 border-radius flex items-center justify-center font-semibold ${
                      isSelected 
                        ? 'bg-white text-accent' 
                        : contact.verified 
                          ? 'bg-success text-white' 
                          : 'bg-tertiary text-primary'
                    }`}>
                      {getContactInitials(contact.deviceId)}
                    </div>

                    {/* Contact Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-medium truncate">
                          {contact.deviceId.slice(0, 16)}...
                        </h3>
                        {lastMessage && (
                          <span className={`text-xs ${
                            isSelected ? 'text-white opacity-75' : 'text-muted'
                          }`}>
                            {formatLastMessageTime(lastMessage.timestamp)}
                          </span>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <p className={`text-sm truncate ${
                          isSelected ? 'text-white opacity-75' : 'text-secondary'
                        }`}>
                          {lastMessage ? (
                            <>
                              {lastMessage.encrypted && '🔒 '}
                              {lastMessage.content}
                            </>
                          ) : (
                            'No messages yet'
                          )}
                        </p>

                        {unreadCount > 0 && (
                          <span className={`ml-2 px-2 py-1 text-xs border-radius font-medium ${
                            isSelected 
                              ? 'bg-white text-accent' 
                              : 'bg-accent text-white'
                          }`}>
                            {unreadCount}
                          </span>
                        )}
                      </div>

                      {/* Verification Status */}
                      {contact.verified && (
                        <div className="flex items-center mt-1">
                          <span className={`text-xs ${
                            isSelected ? 'text-white opacity-75' : 'text-success'
                          }`}>
                            ✓ Verified
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
