import React, { useState, useRef, useEffect } from 'react';
import { useAppStore } from '../../stores/appStore';
import { format } from 'date-fns';

interface MessageViewProps {
  contactId: string;
}

export function MessageView({ contactId }: MessageViewProps) {
  const [messageText, setMessageText] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { 
    messages, 
    sendMessage, 
    deviceIdentity, 
    contacts,
    connectionStatus 
  } = useAppStore();

  const contact = contacts.find(c => c.deviceId === contactId);
  const contactMessages = messages.get(contactId) || [];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [contactMessages]);

  const handleSendMessage = async () => {
    if (!messageText.trim() || isSending) return;

    const text = messageText.trim();
    setMessageText('');
    setIsSending(true);

    try {
      await sendMessage(contactId, text);
    } catch (error) {
      console.error('Failed to send message:', error);
      alert('Failed to send message. Please try again.');
      setMessageText(text); // Restore message text on error
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessageTime = (timestamp: number) => {
    return format(new Date(timestamp), 'HH:mm');
  };

  const isMyMessage = (message: any) => {
    return message.from === deviceIdentity?.deviceId;
  };

  if (!contact) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-secondary">Contact not found</p>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-primary">
      {/* Header */}
      <div className="p-4 border-b border-color bg-secondary">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 border-radius flex items-center justify-center font-semibold ${
            contact.verified ? 'bg-success text-white' : 'bg-tertiary text-primary'
          }`}>
            {contactId.slice(0, 2).toUpperCase()}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold">{contactId.slice(0, 16)}...</h3>
            <div className="flex items-center space-x-2 text-sm text-secondary">
              {contact.verified && (
                <span className="text-success">✓ Verified</span>
              )}
              <span className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-success' : 'bg-danger'
              }`}></span>
              <span>{connectionStatus === 'connected' ? 'Online' : 'Offline'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {contactMessages.length === 0 ? (
          <div className="text-center text-secondary py-8">
            <div className="text-4xl mb-4">👋</div>
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          contactMessages.map((message) => {
            const isMine = isMyMessage(message);
            
            return (
              <div
                key={message.id}
                className={`flex ${isMine ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-xs lg:max-w-md px-4 py-2 border-radius ${
                  isMine 
                    ? 'bg-accent text-white' 
                    : 'bg-secondary text-primary border border-color'
                }`}>
                  <div className="flex items-start space-x-2">
                    {message.encrypted && (
                      <span className="text-xs opacity-75">🔒</span>
                    )}
                    <div className="flex-1">
                      <p className="whitespace-pre-wrap break-words">
                        {message.content}
                      </p>
                      <div className={`text-xs mt-1 ${
                        isMine ? 'text-white opacity-75' : 'text-muted'
                      }`}>
                        {formatMessageTime(message.timestamp)}
                        {message.verified && (
                          <span className="ml-1">✓</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-color bg-secondary">
        <div className="flex space-x-2">
          <textarea
            className="flex-1 input resize-none"
            placeholder={
              connectionStatus === 'connected' 
                ? "Type a message..." 
                : "Connect to send messages"
            }
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={connectionStatus !== 'connected' || isSending}
            rows={1}
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />
          <button
            className="btn btn-primary px-4"
            onClick={handleSendMessage}
            disabled={!messageText.trim() || connectionStatus !== 'connected' || isSending}
          >
            {isSending ? (
              <div className="spinner w-4 h-4"></div>
            ) : (
              '📤'
            )}
          </button>
        </div>
        
        {connectionStatus !== 'connected' && (
          <p className="text-xs text-warning mt-2">
            You're offline. Messages will be sent when connection is restored.
          </p>
        )}
      </div>
    </div>
  );
}
