import { useState } from 'react';
import { useAppStore } from '../../stores/appStore';
import {
  DiscoveredContact,
  ContactRequest,
} from '../../core/discovery/ContactDiscovery';

export function ContactDiscoveryPanel() {
  const [activeSection, setActiveSection] = useState<'discover' | 'requests'>(
    'discover'
  );
  const {
    discoveredContacts,
    contactRequests,
    isDiscovering,
    startDiscovery,
    stopDiscovery,
    sendContactRequest,
    respondToContactRequest,
    addDiscoveredContact,
  } = useAppStore();

  const sections = [
    { id: 'discover' as const, label: 'Discover Contacts', icon: '🔍' },
    {
      id: 'requests' as const,
      label: 'Contact Requests',
      icon: '📨',
      badge: contactRequests.length,
    },
  ];

  const handleStartDiscovery = async () => {
    try {
      await startDiscovery();
    } catch (error) {
      console.error('Failed to start discovery:', error);
      alert('Failed to start discovery. Please try again.');
    }
  };

  const handleStopDiscovery = () => {
    stopDiscovery();
  };

  const handleSendContactRequest = async (contact: DiscoveredContact) => {
    try {
      const message = prompt(
        'Enter a message for your contact request (optional):'
      );
      await sendContactRequest(contact.deviceId, message || undefined);
      alert('Contact request sent!');
    } catch (error) {
      console.error('Failed to send contact request:', error);
      alert('Failed to send contact request. Please try again.');
    }
  };

  const handleAddDirectly = async (contact: DiscoveredContact) => {
    try {
      await addDiscoveredContact(contact);
      alert('Contact added successfully!');
    } catch (error) {
      console.error('Failed to add contact:', error);
      alert('Failed to add contact. Please try again.');
    }
  };

  const handleRespondToRequest = async (
    request: ContactRequest,
    accept: boolean
  ) => {
    try {
      await respondToContactRequest(request.id, accept);
      alert(accept ? 'Contact request accepted!' : 'Contact request rejected.');
    } catch (error) {
      console.error('Failed to respond to contact request:', error);
      alert('Failed to respond to contact request. Please try again.');
    }
  };

  const renderDiscoverSection = () => (
    <div className="p-6 space-y-6">
      {/* Discovery Controls */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Network Discovery</h2>
          <p className="text-sm text-secondary mt-1">
            Find other AirChat users on your local network
          </p>
        </div>
        <div className="card-body">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-medium">Discovery Status</h3>
              <p className="text-sm text-secondary">
                {isDiscovering
                  ? 'Scanning for contacts...'
                  : 'Ready to discover contacts'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  isDiscovering ? 'bg-warning animate-pulse' : 'bg-secondary'
                }`}
              ></div>
              <span className="text-sm font-medium">
                {isDiscovering ? 'Discovering' : 'Idle'}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            {!isDiscovering ? (
              <button
                className="btn btn-primary w-full"
                onClick={handleStartDiscovery}
              >
                🔍 Start Discovery
              </button>
            ) : (
              <button
                className="btn btn-secondary w-full"
                onClick={handleStopDiscovery}
              >
                ⏹️ Stop Discovery
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Discovered Contacts */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">
            Discovered Contacts ({discoveredContacts.length})
          </h2>
        </div>
        <div className="card-body">
          {discoveredContacts.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="font-medium mb-2">No contacts discovered</h3>
              <p className="text-secondary text-sm">
                {isDiscovering
                  ? 'Scanning for contacts...'
                  : 'Start discovery to find nearby AirChat users'}
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {discoveredContacts.map(contact => (
                <div
                  key={contact.deviceId}
                  className="border border-color border-radius p-4"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-12 h-12 border-radius flex items-center justify-center font-semibold ${
                        contact.verified
                          ? 'bg-success text-white'
                          : 'bg-tertiary text-primary'
                      }`}
                    >
                      {contact.deviceId.slice(0, 2).toUpperCase()}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate">
                        {contact.deviceName ||
                          `Device ${contact.deviceId.slice(0, 8)}`}
                      </h3>
                      <div className="text-sm text-secondary space-y-1">
                        <p>ID: {contact.deviceId.slice(0, 16)}...</p>
                        <div className="flex items-center space-x-4">
                          <span>Method: {contact.discoveryMethod}</span>
                          <span>
                            Last seen:{' '}
                            {new Date(contact.lastSeen).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleSendContactRequest(contact)}
                      >
                        📨 Request
                      </button>
                      <button
                        className="btn btn-sm btn-secondary"
                        onClick={() => handleAddDirectly(contact)}
                      >
                        ➕ Add Direct
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Discovery Info */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">How Discovery Works</h2>
        </div>
        <div className="card-body">
          <ul className="text-sm text-secondary space-y-2">
            <li>
              • Discovery scans your local network for other AirChat users
            </li>
            <li>• Only public device identities are shared during discovery</li>
            <li>• You can send contact requests to discovered users</li>
            <li>• Direct adding skips the request process (less secure)</li>
            <li>• All communications remain end-to-end encrypted</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderRequestsSection = () => (
    <div className="p-6 space-y-6">
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">
            Contact Requests ({contactRequests.length})
          </h2>
        </div>
        <div className="card-body">
          {contactRequests.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📨</div>
              <h3 className="font-medium mb-2">No contact requests</h3>
              <p className="text-secondary text-sm">
                Contact requests from other users will appear here
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {contactRequests.map(request => (
                <div
                  key={request.id}
                  className="border border-color border-radius p-4"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 bg-accent border-radius flex items-center justify-center font-semibold text-white">
                      {request.fromDeviceId.slice(0, 2).toUpperCase()}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium">
                        {request.fromDeviceName ||
                          `Device ${request.fromDeviceId.slice(0, 8)}`}
                      </h3>
                      <p className="text-sm text-secondary mb-2">
                        From: {request.fromDeviceId.slice(0, 16)}...
                      </p>
                      {request.message && (
                        <div className="bg-secondary p-3 border-radius mb-3">
                          <p className="text-sm">"{request.message}"</p>
                        </div>
                      )}
                      <div className="text-xs text-muted">
                        Received: {new Date(request.timestamp).toLocaleString()}
                      </div>
                    </div>

                    {request.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => handleRespondToRequest(request, true)}
                        >
                          ✅ Accept
                        </button>
                        <button
                          className="btn btn-sm btn-secondary"
                          onClick={() => handleRespondToRequest(request, false)}
                        >
                          ❌ Reject
                        </button>
                      </div>
                    )}

                    {request.status !== 'pending' && (
                      <div
                        className={`px-3 py-1 border-radius text-sm font-medium ${
                          request.status === 'accepted'
                            ? 'bg-success text-white'
                            : 'bg-danger text-white'
                        }`}
                      >
                        {request.status === 'accepted'
                          ? '✅ Accepted'
                          : '❌ Rejected'}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 flex flex-col bg-primary">
      {/* Header */}
      <div className="p-4 border-b border-color">
        <h1 className="text-2xl font-bold mb-4">Contact Discovery</h1>

        {/* Section Tabs */}
        <div className="flex space-x-1 bg-secondary border-radius p-1">
          {sections.map(section => (
            <button
              key={section.id}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 border-radius transition ${
                activeSection === section.id
                  ? 'bg-primary shadow text-primary'
                  : 'text-secondary hover:text-primary'
              }`}
              onClick={() => setActiveSection(section.id)}
            >
              <span>{section.icon}</span>
              <span className="text-sm font-medium">{section.label}</span>
              {section.badge && section.badge > 0 && (
                <span
                  className={`px-2 py-1 text-xs border-radius font-medium ${
                    activeSection === section.id
                      ? 'bg-accent text-white'
                      : 'bg-accent text-white'
                  }`}
                >
                  {section.badge}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeSection === 'discover'
          ? renderDiscoverSection()
          : renderRequestsSection()}
      </div>
    </div>
  );
}
