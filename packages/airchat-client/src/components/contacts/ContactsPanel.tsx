import { useState } from 'react';
import { useAppStore } from '../../stores/appStore';
import { QRCodeGenerator } from './QRCodeGenerator';
import { QRCodeScanner } from './QRCodeScanner';
import { ContactDiscoveryPanel } from './ContactDiscoveryPanel';

export function ContactsPanel() {
  const [activeTab, setActiveTab] = useState<
    'list' | 'add' | 'share' | 'discover'
  >('list');
  const {} = useAppStore();

  const tabs = [
    { id: 'list' as const, label: 'My Contacts', icon: '👥' },
    { id: 'discover' as const, label: 'Discover', icon: '🔍' },
    { id: 'add' as const, label: 'Add Contact', icon: '📷' },
    { id: 'share' as const, label: 'Share My QR', icon: '📱' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'list':
        return <ContactsList />;
      case 'discover':
        return <ContactDiscoveryPanel />;
      case 'add':
        return <QRCodeScanner />;
      case 'share':
        return <QRCodeGenerator />;
      default:
        return <ContactsList />;
    }
  };

  return (
    <div className="flex-1 flex flex-col bg-primary">
      {/* Header */}
      <div className="p-4 border-b border-color">
        <h1 className="text-2xl font-bold mb-4">Contacts</h1>

        {/* Tabs */}
        <div className="flex space-x-1 bg-secondary border-radius p-1">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 border-radius transition ${
                activeTab === tab.id
                  ? 'bg-primary shadow text-primary'
                  : 'text-secondary hover:text-primary'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span>{tab.icon}</span>
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">{renderTabContent()}</div>
    </div>
  );
}

function ContactsList() {
  const { contacts, deviceIdentity } = useAppStore();

  if (contacts.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">👥</div>
          <h2 className="text-xl font-semibold mb-2">No contacts yet</h2>
          <p className="text-secondary mb-6">
            Add your first contact by scanning their QR code or sharing yours.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="space-y-3">
        {contacts.map(contact => (
          <div key={contact.deviceId} className="card p-4">
            <div className="flex items-center space-x-3">
              <div
                className={`w-12 h-12 border-radius flex items-center justify-center font-semibold ${
                  contact.verified
                    ? 'bg-success text-white'
                    : 'bg-tertiary text-primary'
                }`}
              >
                {contact.deviceId.slice(0, 2).toUpperCase()}
              </div>

              <div className="flex-1 min-w-0">
                <h3 className="font-medium truncate">{contact.deviceId}</h3>
                <div className="flex items-center space-x-2 text-sm text-secondary">
                  <span>
                    Added {new Date(contact.addedAt).toLocaleDateString()}
                  </span>
                  {contact.verified && (
                    <span className="text-success">✓ Verified</span>
                  )}
                </div>
                <div className="text-xs text-muted mt-1">
                  Fingerprint: {contact.fingerprint}
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() => {
                    useAppStore.getState().selectContact(contact.deviceId);
                    useAppStore.getState().setCurrentView('chat');
                  }}
                >
                  Message
                </button>

                {!contact.verified && (
                  <button
                    className="btn btn-sm btn-secondary"
                    onClick={() => {
                      // TODO: Implement verification flow
                      alert('Verification flow not implemented yet');
                    }}
                  >
                    Verify
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
