import React, { useEffect, useRef, useState } from 'react';
import QRCode from 'qrcode';
import { useAppStore } from '../../stores/appStore';

export function QRCodeGenerator() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [qrData, setQrData] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const { deviceIdentity } = useAppStore();

  useEffect(() => {
    generateQRCode();
  }, [deviceIdentity]);

  const generateQRCode = async () => {
    if (!deviceIdentity || !canvasRef.current) return;

    setIsGenerating(true);
    try {
      // Create QR data with device identity
      const qrPayload = {
        type: 'airchat-contact',
        version: '1.0',
        deviceId: deviceIdentity.deviceId,
        publicKey: Array.from(deviceIdentity.publicKey), // Convert Uint8Array to regular array for JSON
        timestamp: Date.now()
      };

      const qrDataString = JSON.stringify(qrPayload);
      setQrData(qrDataString);

      // Generate QR code
      await QRCode.toCanvas(canvasRef.current, qrDataString, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(qrData);
      alert('Contact data copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      alert('Failed to copy to clipboard');
    }
  };

  const downloadQR = () => {
    if (!canvasRef.current) return;

    const link = document.createElement('a');
    link.download = `airchat-contact-${deviceIdentity?.deviceId.slice(0, 8)}.png`;
    link.href = canvasRef.current.toDataURL();
    link.click();
  };

  if (!deviceIdentity) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-secondary">Device identity not available</p>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold mb-2">Share Your Contact</h2>
          <p className="text-secondary">
            Others can scan this QR code to add you as a contact
          </p>
        </div>

        {/* QR Code */}
        <div className="card p-6 text-center mb-6">
          {isGenerating ? (
            <div className="flex items-center justify-center h-64">
              <div className="spinner"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <canvas
                ref={canvasRef}
                className="mx-auto border border-color border-radius"
              />
              
              <div className="text-sm text-secondary">
                <p>Device ID: {deviceIdentity.deviceId.slice(0, 16)}...</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <button
            className="btn btn-primary w-full"
            onClick={copyToClipboard}
            disabled={isGenerating || !qrData}
          >
            📋 Copy Contact Data
          </button>
          
          <button
            className="btn btn-secondary w-full"
            onClick={downloadQR}
            disabled={isGenerating}
          >
            💾 Download QR Code
          </button>
          
          <button
            className="btn btn-secondary w-full"
            onClick={generateQRCode}
            disabled={isGenerating}
          >
            🔄 Regenerate QR Code
          </button>
        </div>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-secondary border-radius">
          <h3 className="font-medium mb-2">How to share:</h3>
          <ul className="text-sm text-secondary space-y-1">
            <li>• Show this QR code to someone nearby</li>
            <li>• Send the downloaded image via any messaging app</li>
            <li>• Copy and paste the contact data</li>
            <li>• The QR code contains your public key for secure messaging</li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="mt-4 p-4 bg-warning bg-opacity-10 border border-warning border-radius">
          <h3 className="font-medium text-warning mb-2">🔒 Privacy Notice</h3>
          <p className="text-sm text-secondary">
            This QR code contains your public key, which is safe to share. 
            Your private key never leaves this device.
          </p>
        </div>
      </div>
    </div>
  );
}
