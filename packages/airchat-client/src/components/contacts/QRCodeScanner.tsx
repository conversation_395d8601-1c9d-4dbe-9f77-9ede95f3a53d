import React, { useState, useRef } from 'react';
import QrScanner from 'qr-scanner';
import { useAppStore } from '../../stores/appStore';

export function QRCodeScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [manualInput, setManualInput] = useState('');
  const [scanMode, setScanMode] = useState<'camera' | 'manual'>('camera');
  const videoRef = useRef<HTMLVideoElement>(null);
  const scannerRef = useRef<QrScanner | null>(null);
  const { addContact } = useAppStore();

  const startScanning = async () => {
    if (!videoRef.current) return;

    try {
      setIsScanning(true);
      
      const scanner = new QrScanner(
        videoRef.current,
        (result) => handleScanResult(result.data),
        {
          highlightScanRegion: true,
          highlightCodeOutline: true,
        }
      );

      scannerRef.current = scanner;
      await scanner.start();
    } catch (error) {
      console.error('Failed to start camera:', error);
      alert('Failed to access camera. Please check permissions or try manual input.');
      setIsScanning(false);
    }
  };

  const stopScanning = () => {
    if (scannerRef.current) {
      scannerRef.current.stop();
      scannerRef.current.destroy();
      scannerRef.current = null;
    }
    setIsScanning(false);
  };

  const handleScanResult = async (data: string) => {
    try {
      stopScanning();
      await processContactData(data);
    } catch (error) {
      console.error('Failed to process scan result:', error);
      alert('Invalid QR code. Please try again.');
      if (scanMode === 'camera') {
        startScanning(); // Restart scanning
      }
    }
  };

  const handleManualSubmit = async () => {
    if (!manualInput.trim()) return;

    try {
      await processContactData(manualInput.trim());
      setManualInput('');
    } catch (error) {
      console.error('Failed to process manual input:', error);
      alert('Invalid contact data. Please check the format and try again.');
    }
  };

  const processContactData = async (data: string) => {
    try {
      const contactData = JSON.parse(data);
      
      // Validate contact data format
      if (
        contactData.type !== 'airchat-contact' ||
        !contactData.deviceId ||
        !contactData.publicKey ||
        !Array.isArray(contactData.publicKey)
      ) {
        throw new Error('Invalid contact data format');
      }

      // Convert public key back to Uint8Array
      const publicKey = new Uint8Array(contactData.publicKey);
      
      // Add contact
      await addContact(contactData.deviceId, publicKey, false);
      
      alert(`Contact added successfully!\nDevice ID: ${contactData.deviceId.slice(0, 16)}...`);
      
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error('Invalid JSON format');
      }
      throw error;
    }
  };

  return (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold mb-2">Add New Contact</h2>
          <p className="text-secondary">
            Scan a QR code or paste contact data manually
          </p>
        </div>

        {/* Mode Toggle */}
        <div className="flex space-x-1 bg-secondary border-radius p-1 mb-6">
          <button
            className={`flex-1 py-2 px-3 border-radius transition ${
              scanMode === 'camera'
                ? 'bg-primary shadow text-primary'
                : 'text-secondary hover:text-primary'
            }`}
            onClick={() => {
              setScanMode('camera');
              if (isScanning) stopScanning();
            }}
          >
            📷 Camera
          </button>
          <button
            className={`flex-1 py-2 px-3 border-radius transition ${
              scanMode === 'manual'
                ? 'bg-primary shadow text-primary'
                : 'text-secondary hover:text-primary'
            }`}
            onClick={() => {
              setScanMode('manual');
              if (isScanning) stopScanning();
            }}
          >
            ⌨️ Manual
          </button>
        </div>

        {/* Camera Scanner */}
        {scanMode === 'camera' && (
          <div className="space-y-4">
            <div className="card p-4">
              <video
                ref={videoRef}
                className="w-full border-radius"
                style={{ maxHeight: '300px' }}
              />
              
              {!isScanning && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 border-radius">
                  <div className="text-center text-white">
                    <div className="text-4xl mb-2">📷</div>
                    <p>Camera preview</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              {!isScanning ? (
                <button
                  className="btn btn-primary w-full"
                  onClick={startScanning}
                >
                  Start Scanning
                </button>
              ) : (
                <button
                  className="btn btn-secondary w-full"
                  onClick={stopScanning}
                >
                  Stop Scanning
                </button>
              )}
            </div>

            <div className="p-4 bg-secondary border-radius">
              <h3 className="font-medium mb-2">Instructions:</h3>
              <ul className="text-sm text-secondary space-y-1">
                <li>• Point your camera at the QR code</li>
                <li>• Make sure the code is well-lit and in focus</li>
                <li>• The contact will be added automatically when detected</li>
              </ul>
            </div>
          </div>
        )}

        {/* Manual Input */}
        {scanMode === 'manual' && (
          <div className="space-y-4">
            <div>
              <label htmlFor="contactData" className="block text-sm font-medium mb-2">
                Contact Data
              </label>
              <textarea
                id="contactData"
                className="input"
                rows={6}
                placeholder="Paste the contact data here..."
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value)}
              />
            </div>

            <button
              className="btn btn-primary w-full"
              onClick={handleManualSubmit}
              disabled={!manualInput.trim()}
            >
              Add Contact
            </button>

            <div className="p-4 bg-secondary border-radius">
              <h3 className="font-medium mb-2">Manual Input:</h3>
              <ul className="text-sm text-secondary space-y-1">
                <li>• Copy contact data from a message or email</li>
                <li>• Paste the complete JSON data above</li>
                <li>• The data should start with {"{"} and end with {"}"}</li>
              </ul>
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-success bg-opacity-10 border border-success border-radius">
          <h3 className="font-medium text-success mb-2">🔒 Security</h3>
          <p className="text-sm text-secondary">
            Only add contacts from trusted sources. Verify the contact's 
            identity through a secure channel before sending sensitive information.
          </p>
        </div>
      </div>
    </div>
  );
}
