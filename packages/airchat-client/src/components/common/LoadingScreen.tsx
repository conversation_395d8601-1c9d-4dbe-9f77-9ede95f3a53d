import React from 'react';

interface LoadingScreenProps {
  message?: string;
}

export function LoadingScreen({ message = 'Loading...' }: LoadingScreenProps) {
  return (
    <div className="flex flex-col items-center justify-center h-full bg-primary">
      <div className="text-center">
        <div className="spinner mb-4 mx-auto" style={{ width: '40px', height: '40px' }}></div>
        <h2 className="text-xl font-semibold mb-2">AirChat</h2>
        <p className="text-secondary">{message}</p>
      </div>
    </div>
  );
}
