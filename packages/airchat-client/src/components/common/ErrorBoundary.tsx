import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-full bg-primary p-6">
          <div className="text-center max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-danger">Something went wrong</h2>
            <p className="text-secondary mb-6">
              An unexpected error occurred. Please refresh the page to try again.
            </p>
            <div className="space-y-2">
              <button
                className="btn btn-primary w-full"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </button>
              <button
                className="btn btn-secondary w-full"
                onClick={() => this.setState({ hasError: false })}
              >
                Try Again
              </button>
            </div>
            {this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-muted">
                  Error Details
                </summary>
                <pre className="mt-2 text-xs bg-secondary p-2 border-radius overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
