import React, { useState } from 'react';
import { useAppStore } from '../../stores/appStore';

export function DeviceSetup() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [deviceName, setDeviceName] = useState('');
  const { initializeApp } = useAppStore();

  const handleSetupDevice = async () => {
    if (!deviceName.trim()) {
      alert('Please enter a device name');
      return;
    }

    setIsGenerating(true);
    try {
      await initializeApp();
    } catch (error) {
      console.error('Failed to setup device:', error);
      alert('Failed to setup device. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex items-center justify-center h-full bg-secondary">
      <div className="card max-w-md w-full m-4">
        <div className="card-header text-center">
          <h1 className="text-2xl font-bold">Welcome to AirChat</h1>
          <p className="text-secondary mt-2">
            Secure, peer-to-peer messaging without servers
          </p>
        </div>
        
        <div className="card-body">
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-4">Setup Your Device</h2>
            <p className="text-secondary text-sm mb-4">
              First, let's create your device identity. This will generate a unique 
              encryption key pair for secure messaging.
            </p>
            
            <div className="mb-4">
              <label htmlFor="deviceName" className="block text-sm font-medium mb-2">
                Device Name
              </label>
              <input
                id="deviceName"
                type="text"
                className="input"
                placeholder="e.g., John's iPhone"
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                disabled={isGenerating}
              />
              <p className="text-xs text-muted mt-1">
                This helps you identify this device when managing contacts
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <button
              className="btn btn-primary w-full"
              onClick={handleSetupDevice}
              disabled={isGenerating || !deviceName.trim()}
            >
              {isGenerating ? (
                <span className="flex items-center">
                  <div className="spinner mr-2"></div>
                  Generating Keys...
                </span>
              ) : (
                'Create Device Identity'
              )}
            </button>
          </div>
        </div>

        <div className="card-footer">
          <div className="text-xs text-muted">
            <h3 className="font-medium mb-2">Privacy & Security:</h3>
            <ul className="space-y-1">
              <li>• Your encryption keys never leave this device</li>
              <li>• All messages are end-to-end encrypted</li>
              <li>• No data is stored on external servers</li>
              <li>• You have full control over your communications</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
