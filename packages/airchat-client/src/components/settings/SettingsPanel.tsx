import React, { useState } from 'react';
import { useAppStore } from '../../stores/appStore';

export function SettingsPanel() {
  const [activeSection, setActiveSection] = useState<'general' | 'privacy' | 'about'>('general');
  const { deviceIdentity, secureMessaging, connectionStatus } = useAppStore();

  const sections = [
    { id: 'general' as const, label: 'General', icon: '⚙️' },
    { id: 'privacy' as const, label: 'Privacy & Security', icon: '🔒' },
    { id: 'about' as const, label: 'About', icon: 'ℹ️' }
  ];

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'general':
        return <GeneralSettings />;
      case 'privacy':
        return <PrivacySettings />;
      case 'about':
        return <AboutSection />;
      default:
        return <GeneralSettings />;
    }
  };

  return (
    <div className="flex-1 flex flex-col bg-primary">
      {/* Header */}
      <div className="p-4 border-b border-color">
        <h1 className="text-2xl font-bold mb-4">Settings</h1>
        
        {/* Section Tabs */}
        <div className="flex space-x-1 bg-secondary border-radius p-1">
          {sections.map((section) => (
            <button
              key={section.id}
              className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 border-radius transition ${
                activeSection === section.id
                  ? 'bg-primary shadow text-primary'
                  : 'text-secondary hover:text-primary'
              }`}
              onClick={() => setActiveSection(section.id)}
            >
              <span>{section.icon}</span>
              <span className="text-sm font-medium">{section.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {renderSectionContent()}
      </div>
    </div>
  );
}

function GeneralSettings() {
  const { connectionStatus, disconnect } = useAppStore();

  return (
    <div className="p-6 space-y-6">
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Connection</h2>
        </div>
        <div className="card-body space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Connection Status</h3>
              <p className="text-sm text-secondary">Current network connection state</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-success' : 
                connectionStatus === 'connecting' ? 'bg-warning' : 'bg-danger'
              }`}></div>
              <span className="text-sm font-medium capitalize">{connectionStatus}</span>
            </div>
          </div>
          
          {connectionStatus === 'connected' && (
            <button
              className="btn btn-secondary"
              onClick={disconnect}
            >
              Disconnect
            </button>
          )}
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Appearance</h2>
        </div>
        <div className="card-body space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Theme</h3>
              <p className="text-sm text-secondary">Choose your preferred theme</p>
            </div>
            <select className="input w-32">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}

function PrivacySettings() {
  const { deviceIdentity, secureMessaging } = useAppStore();
  const [showBackupDialog, setShowBackupDialog] = useState(false);

  const createBackup = async () => {
    if (!secureMessaging) return;

    const password = prompt('Enter a password to encrypt your backup:');
    if (!password) return;

    try {
      const backup = await secureMessaging.createKeyBackup(password);
      
      // Download backup file
      const blob = new Blob([backup], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `airchat-backup-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      alert('Backup created and downloaded successfully!');
    } catch (error) {
      console.error('Failed to create backup:', error);
      alert('Failed to create backup. Please try again.');
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Device Identity</h2>
        </div>
        <div className="card-body space-y-4">
          <div>
            <h3 className="font-medium mb-2">Device ID</h3>
            <div className="p-3 bg-secondary border-radius font-mono text-sm break-all">
              {deviceIdentity?.deviceId || 'Not available'}
            </div>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Public Key Fingerprint</h3>
            <div className="p-3 bg-secondary border-radius font-mono text-sm break-all">
              {deviceIdentity?.publicKey ? 
                Array.from(deviceIdentity.publicKey.slice(0, 16))
                  .map(b => b.toString(16).padStart(2, '0'))
                  .join(' ') + '...'
                : 'Not available'
              }
            </div>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Backup & Recovery</h2>
        </div>
        <div className="card-body space-y-4">
          <div>
            <h3 className="font-medium mb-2">Create Backup</h3>
            <p className="text-sm text-secondary mb-4">
              Create an encrypted backup of your device keys and contacts. 
              Store this backup safely - you'll need it to restore your identity on a new device.
            </p>
            <button
              className="btn btn-primary"
              onClick={createBackup}
            >
              Create Encrypted Backup
            </button>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Security</h2>
        </div>
        <div className="card-body space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Auto-encrypt messages</h3>
              <p className="text-sm text-secondary">All messages are encrypted by default</p>
            </div>
            <input type="checkbox" checked disabled />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Require contact verification</h3>
              <p className="text-sm text-secondary">Only allow messages from verified contacts</p>
            </div>
            <input type="checkbox" />
          </div>
        </div>
      </div>
    </div>
  );
}

function AboutSection() {
  return (
    <div className="p-6 space-y-6">
      <div className="card">
        <div className="card-body text-center">
          <div className="text-6xl mb-4">📱</div>
          <h2 className="text-2xl font-bold mb-2">AirChat</h2>
          <p className="text-secondary mb-4">Version 1.0.0</p>
          <p className="text-sm text-secondary">
            Secure, peer-to-peer messaging without servers
          </p>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Features</h2>
        </div>
        <div className="card-body">
          <ul className="space-y-2 text-sm">
            <li className="flex items-center space-x-2">
              <span className="text-success">✓</span>
              <span>End-to-end encryption</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-success">✓</span>
              <span>Peer-to-peer messaging</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-success">✓</span>
              <span>No central servers</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-success">✓</span>
              <span>QR code contact sharing</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-success">✓</span>
              <span>Offline message queue</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-muted">○</span>
              <span className="text-muted">File sharing (coming soon)</span>
            </li>
            <li className="flex items-center space-x-2">
              <span className="text-muted">○</span>
              <span className="text-muted">Voice calls (coming soon)</span>
            </li>
          </ul>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold">Privacy & Security</h2>
        </div>
        <div className="card-body">
          <ul className="space-y-2 text-sm text-secondary">
            <li>• All messages are encrypted end-to-end</li>
            <li>• Your private keys never leave this device</li>
            <li>• No data is stored on external servers</li>
            <li>• Perfect forward secrecy protects past messages</li>
            <li>• Open source and auditable</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
