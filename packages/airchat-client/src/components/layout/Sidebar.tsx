import React from 'react';
import { useAppStore } from '../../stores/appStore';

export function Sidebar() {
  const { 
    currentView, 
    setCurrentView, 
    deviceIdentity, 
    connectionStatus,
    contacts,
    unreadCounts 
  } = useAppStore();

  const totalUnread = Array.from(unreadCounts.values()).reduce((sum, count) => sum + count, 0);

  const menuItems = [
    {
      id: 'chat' as const,
      label: 'Messages',
      icon: '💬',
      badge: totalUnread > 0 ? totalUnread : undefined
    },
    {
      id: 'contacts' as const,
      label: 'Contacts',
      icon: '👥',
      badge: contacts.length > 0 ? contacts.length : undefined
    },
    {
      id: 'settings' as const,
      label: 'Settings',
      icon: '⚙️'
    }
  ];

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-success';
      case 'connecting':
        return 'bg-warning';
      default:
        return 'bg-danger';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      default:
        return 'Offline';
    }
  };

  return (
    <div className="w-64 bg-secondary border-r border-color flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-color">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-accent border-radius flex items-center justify-center text-white font-bold">
            {deviceIdentity?.deviceId.slice(0, 2).toUpperCase() || 'AC'}
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="font-semibold text-sm truncate">AirChat</h2>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${getConnectionStatusColor()}`}></div>
              <span className="text-xs text-secondary">{getConnectionStatusText()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-2">
        <ul className="space-y-1">
          {menuItems.map((item) => (
            <li key={item.id}>
              <button
                className={`w-full flex items-center space-x-3 p-3 border-radius transition ${
                  currentView === item.id
                    ? 'bg-accent text-white'
                    : 'text-primary hover:bg-tertiary'
                }`}
                onClick={() => setCurrentView(item.id)}
              >
                <span className="text-lg">{item.icon}</span>
                <span className="flex-1 text-left font-medium">{item.label}</span>
                {item.badge && (
                  <span className={`px-2 py-1 text-xs border-radius font-medium ${
                    currentView === item.id
                      ? 'bg-white text-accent'
                      : 'bg-accent text-white'
                  }`}>
                    {item.badge}
                  </span>
                )}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Device Info */}
      <div className="p-4 border-t border-color">
        <div className="text-xs text-muted">
          <div className="mb-1">
            <span className="font-medium">Device ID:</span>
          </div>
          <div className="font-mono bg-tertiary p-2 border-radius break-all">
            {deviceIdentity?.deviceId.slice(0, 16) || 'Unknown'}...
          </div>
        </div>
      </div>
    </div>
  );
}
