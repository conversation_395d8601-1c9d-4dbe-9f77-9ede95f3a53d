import React from 'react';
import { useAppStore } from '../../stores/appStore';
import { Sidebar } from './Sidebar';
import { ChatArea } from '../chat/ChatArea';
import { ContactsPanel } from '../contacts/ContactsPanel';
import { SettingsPanel } from '../settings/SettingsPanel';

export function MainInterface() {
  const { currentView } = useAppStore();

  const renderMainContent = () => {
    switch (currentView) {
      case 'chat':
        return <ChatArea />;
      case 'contacts':
        return <ContactsPanel />;
      case 'settings':
        return <SettingsPanel />;
      default:
        return <ChatArea />;
    }
  };

  return (
    <div className="flex h-full">
      <Sidebar />
      <main className="flex-1 flex flex-col">
        {renderMainContent()}
      </main>
    </div>
  );
}
