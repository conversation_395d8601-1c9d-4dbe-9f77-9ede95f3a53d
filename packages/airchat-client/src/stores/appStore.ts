import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { SecureMessaging } from '../core/messaging/SecureMessaging';
import { P2PConnectionManager } from '../core/p2p/P2PConnectionManager';
import { SignalingClient } from '../core/p2p/SignalingClient';
import { DeviceIdentity } from '../core/crypto/E2EEncryption';
import { ContactKey } from '../core/crypto/KeyManager';
import { SecureMessage } from '../core/messaging/SecureMessaging';
import {
  ContactDiscovery,
  DiscoveredContact,
  ContactRequest,
} from '../core/discovery/ContactDiscovery';

export interface AppState {
  // Initialization
  isInitialized: boolean;
  deviceIdentity: DeviceIdentity | null;

  // Core services
  secureMessaging: SecureMessaging | null;
  connectionManager: P2PConnectionManager | null;
  signalingClient: SignalingClient | null;
  contactDiscovery: ContactDiscovery | null;

  // UI state
  currentView: 'setup' | 'chat' | 'contacts' | 'settings';
  selectedContact: string | null;
  isConnecting: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected';

  // Data
  contacts: ContactKey[];
  messages: Map<string, SecureMessage[]>;
  unreadCounts: Map<string, number>;

  // Discovery
  discoveredContacts: DiscoveredContact[];
  contactRequests: ContactRequest[];
  isDiscovering: boolean;

  // Actions
  initializeApp: () => Promise<void>;
  setCurrentView: (view: AppState['currentView']) => void;
  selectContact: (contactId: string | null) => void;
  addContact: (
    deviceId: string,
    publicKey: Uint8Array,
    verified?: boolean
  ) => Promise<void>;
  sendMessage: (to: string, content: string) => Promise<void>;
  markMessagesAsRead: (contactId: string) => void;
  connectToPeer: (peerId: string) => Promise<void>;
  disconnect: () => void;

  // Discovery actions
  startDiscovery: () => Promise<void>;
  stopDiscovery: () => void;
  sendContactRequest: (deviceId: string, message?: string) => Promise<void>;
  respondToContactRequest: (
    requestId: string,
    accept: boolean
  ) => Promise<void>;
  addDiscoveredContact: (contact: DiscoveredContact) => Promise<void>;
}

export const useAppStore = create<AppState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    isInitialized: false,
    deviceIdentity: null,
    secureMessaging: null,
    connectionManager: null,
    signalingClient: null,
    contactDiscovery: null,
    currentView: 'setup',
    selectedContact: null,
    isConnecting: false,
    connectionStatus: 'disconnected',
    contacts: [],
    messages: new Map(),
    unreadCounts: new Map(),
    discoveredContacts: [],
    contactRequests: [],
    isDiscovering: false,

    // Initialize the application
    initializeApp: async () => {
      try {
        // Create signaling client
        const signalingClient = new SignalingClient(
          'wss://airchat-signaling.your-domain.workers.dev'
        );

        // Create connection manager
        const connectionManager = new P2PConnectionManager();

        // Create secure messaging
        const secureMessaging = new SecureMessaging(connectionManager);

        // Initialize secure messaging (this will create or load device identity)
        const deviceIdentity = await secureMessaging.initialize();

        // Create contact discovery
        const contactDiscovery = new ContactDiscovery(connectionManager);
        await contactDiscovery.initialize(deviceIdentity);

        // Load contacts
        const contacts = secureMessaging.getAllContacts();

        // Set up event listeners
        setupEventListeners(
          secureMessaging,
          connectionManager,
          signalingClient,
          contactDiscovery,
          set,
          get
        );

        set({
          isInitialized: true,
          deviceIdentity,
          secureMessaging,
          connectionManager,
          signalingClient,
          contactDiscovery,
          contacts,
          currentView: 'chat',
        });

        console.log('App initialized successfully', {
          deviceId: deviceIdentity.deviceId,
        });
      } catch (error) {
        console.error('Failed to initialize app:', error);
        throw error;
      }
    },

    // UI actions
    setCurrentView: view => set({ currentView: view }),

    selectContact: contactId => {
      set({ selectedContact: contactId });
      if (contactId) {
        get().markMessagesAsRead(contactId);
      }
    },

    // Contact management
    addContact: async (deviceId, publicKey, verified = false) => {
      const { secureMessaging } = get();
      if (!secureMessaging) throw new Error('Secure messaging not initialized');

      await secureMessaging.addContact(deviceId, publicKey, verified);
      const contacts = secureMessaging.getAllContacts();
      set({ contacts });
    },

    // Messaging
    sendMessage: async (to, content) => {
      const { secureMessaging, messages } = get();
      if (!secureMessaging) throw new Error('Secure messaging not initialized');

      const messageId = await secureMessaging.sendMessage(to, content);

      // Optimistically add message to local state
      const currentMessages = messages.get(to) || [];
      const newMessage: SecureMessage = {
        id: messageId,
        from: secureMessaging.getDeviceIdentity()?.deviceId || 'unknown',
        to,
        content,
        timestamp: Date.now(),
        type: 'text',
        encrypted: true,
      };

      const updatedMessages = new Map(messages);
      updatedMessages.set(to, [...currentMessages, newMessage]);
      set({ messages: updatedMessages });
    },

    markMessagesAsRead: contactId => {
      const { unreadCounts } = get();
      const updatedCounts = new Map(unreadCounts);
      updatedCounts.delete(contactId);
      set({ unreadCounts: updatedCounts });
    },

    // Connection management
    connectToPeer: async peerId => {
      const { connectionManager } = get();
      if (!connectionManager)
        throw new Error('Connection manager not initialized');

      set({ isConnecting: true, connectionStatus: 'connecting' });

      try {
        await connectionManager.createConnection(peerId, true);
        set({ connectionStatus: 'connected' });
      } catch (error) {
        set({ connectionStatus: 'disconnected' });
        throw error;
      } finally {
        set({ isConnecting: false });
      }
    },

    disconnect: () => {
      const { connectionManager } = get();
      if (connectionManager) {
        // Close all connections individually
        for (const [peerId] of connectionManager['connections']) {
          connectionManager.closeConnection(peerId);
        }
      }
      set({ connectionStatus: 'disconnected' });
    },

    // Discovery actions
    startDiscovery: async () => {
      const { contactDiscovery } = get();
      if (!contactDiscovery)
        throw new Error('Contact discovery not initialized');

      set({ isDiscovering: true });
      try {
        await contactDiscovery.startDiscovery();
      } catch (error) {
        set({ isDiscovering: false });
        throw error;
      }
    },

    stopDiscovery: () => {
      const { contactDiscovery } = get();
      if (contactDiscovery) {
        contactDiscovery.stopDiscovery();
      }
      set({ isDiscovering: false });
    },

    sendContactRequest: async (deviceId: string, message?: string) => {
      const { contactDiscovery } = get();
      if (!contactDiscovery)
        throw new Error('Contact discovery not initialized');

      await contactDiscovery.sendContactRequest(deviceId, message);
    },

    respondToContactRequest: async (requestId: string, accept: boolean) => {
      const { contactDiscovery } = get();
      if (!contactDiscovery)
        throw new Error('Contact discovery not initialized');

      await contactDiscovery.respondToContactRequest(requestId, accept);
    },

    addDiscoveredContact: async (contact: DiscoveredContact) => {
      const { addContact } = get();
      await addContact(contact.deviceId, contact.publicKey, contact.verified);
    },
  }))
);

// Set up event listeners for the core services
function setupEventListeners(
  secureMessaging: SecureMessaging,
  connectionManager: P2PConnectionManager,
  _signalingClient: SignalingClient,
  contactDiscovery: ContactDiscovery,
  set: any,
  get: any
) {
  // Message received
  secureMessaging.addEventListener('message-received', (event: any) => {
    const { message } = event.detail;
    const { messages, unreadCounts, selectedContact } = get();

    const contactId = message.from;
    const currentMessages = messages.get(contactId) || [];
    const updatedMessages = new Map(messages);
    updatedMessages.set(contactId, [...currentMessages, message]);

    // Update unread count if not currently viewing this contact
    const updatedUnreadCounts = new Map(unreadCounts);
    if (selectedContact !== contactId) {
      const currentCount = updatedUnreadCounts.get(contactId) || 0;
      updatedUnreadCounts.set(contactId, (currentCount as number) + 1);
    }

    set({
      messages: updatedMessages,
      unreadCounts: updatedUnreadCounts,
    });
  });

  // Connection status changes
  connectionManager.addEventListener(
    'connection-state-changed',
    (event: any) => {
      const { peerId, state } = event.detail;
      console.log(`Connection to ${peerId}: ${state}`);

      if (state === 'connected') {
        set({ connectionStatus: 'connected' });
      } else if (state === 'disconnected') {
        set({ connectionStatus: 'disconnected' });
      }
    }
  );

  // Contact added
  secureMessaging.addEventListener('contact-added', () => {
    const contacts = secureMessaging.getAllContacts();
    set({ contacts });
  });

  // Discovery events
  contactDiscovery.addEventListener('contact-discovered', (event: any) => {
    const { contact } = event.detail;
    const { discoveredContacts } = get();

    // Check if contact already exists
    const existingIndex = discoveredContacts.findIndex(
      (c: DiscoveredContact) => c.deviceId === contact.deviceId
    );
    let updatedContacts;

    if (existingIndex >= 0) {
      // Update existing contact
      updatedContacts = [...discoveredContacts];
      updatedContacts[existingIndex] = contact;
    } else {
      // Add new contact
      updatedContacts = [...discoveredContacts, contact];
    }

    set({ discoveredContacts: updatedContacts });
  });

  contactDiscovery.addEventListener(
    'contact-request-received',
    (event: any) => {
      const { request } = event.detail;
      const { contactRequests } = get();

      set({
        contactRequests: [...contactRequests, request],
      });
    }
  );

  contactDiscovery.addEventListener('discovery-stopped', () => {
    set({ isDiscovering: false });
  });
}
