// Use the global EventTarget available in modern browsers and Node.js
import { KeyManager, DeviceIdentity } from './core/crypto/KeyManager';
import {
  P2PConnectionManager,
  ConnectionQuality,
} from './core/p2p/P2PConnectionManager';
import {
  ContactDiscovery,
  DiscoveredContact,
} from './core/discovery/ContactDiscovery';
import { SecureMessaging } from './core/messaging/SecureMessaging';
import { MessageContent } from '@airchat/shared';

export interface AirChatConfig {
  iceServers?: RTCIceServer[];
  discoveryConfig?: {
    broadcastInterval?: number;
    discoveryTimeout?: number;
  };
  messagingConfig?: {
    maxRetries?: number;
    retryDelay?: number;
  };
}

/**
 * Main AirChat client class that orchestrates all components
 */
export class AirChatClient extends EventTarget {
  private keyManager: KeyManager;
  private p2pManager: P2PConnectionManager;
  private contactDiscovery: ContactDiscovery;
  private secureMessaging: SecureMessaging;
  private config: AirChatConfig;
  private initialized = false;

  constructor(config: AirChatConfig = {}) {
    super();
    this.config = config;

    // Initialize core components
    this.keyManager = new KeyManager();
    this.p2pManager = new P2PConnectionManager({
      iceServers: config.iceServers || [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    });
    this.contactDiscovery = new ContactDiscovery(
      this.p2pManager,
      config.discoveryConfig
    );
    this.secureMessaging = new SecureMessaging(
      this.keyManager,
      this.p2pManager,
      config.messagingConfig
    );

    this.setupEventHandlers();
  }

  /**
   * Initialize the AirChat client
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Initialize device identity
      let deviceIdentity = await this.keyManager.loadDeviceIdentity();
      if (!deviceIdentity) {
        deviceIdentity = await this.keyManager.generateDeviceIdentity();
        await this.keyManager.storeDeviceIdentity(deviceIdentity);
      }

      // Initialize components with device identity
      await this.contactDiscovery.initialize(deviceIdentity);
      await this.secureMessaging.initialize();

      this.initialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize AirChat client:', error);
      throw error;
    }
  }

  /**
   * Start contact discovery
   */
  async startDiscovery(): Promise<void> {
    if (!this.initialized) {
      throw new Error('Client not initialized');
    }
    await this.contactDiscovery.startDiscovery();
  }

  /**
   * Stop contact discovery
   */
  async stopDiscovery(): Promise<void> {
    await this.contactDiscovery.stopDiscovery();
  }

  /**
   * Get discovered contacts
   */
  getDiscoveredContacts(): Map<string, DiscoveredContact> {
    return this.contactDiscovery.getDiscoveredContacts();
  }

  /**
   * Send a contact request
   */
  async sendContactRequest(deviceId: string, message: string): Promise<string> {
    return await this.contactDiscovery.sendContactRequest(deviceId, message);
  }

  /**
   * Accept a contact request
   */
  async acceptContactRequest(requestId: string): Promise<void> {
    await this.contactDiscovery.acceptContactRequest(requestId);
  }

  /**
   * Reject a contact request
   */
  async rejectContactRequest(requestId: string): Promise<void> {
    await this.contactDiscovery.rejectContactRequest(requestId);
  }

  /**
   * Connect to a peer
   */
  async connectToPeer(peerId: string, maxRetries = 3): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.p2pManager.createConnection(peerId, true);
        return;
      } catch (error) {
        lastError = error as Error;
        console.warn(`Connection attempt ${attempt} failed:`, error);

        if (attempt < maxRetries) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw lastError || new Error('Failed to connect after retries');
  }

  /**
   * Check if connected to a peer
   */
  isConnectedToPeer(peerId: string): boolean {
    return this.p2pManager.isConnected(peerId);
  }

  /**
   * Get connection quality for a peer
   */
  getConnectionQuality(peerId: string): ConnectionQuality | undefined {
    return this.p2pManager.getConnectionQuality(peerId);
  }

  /**
   * Get all active connections
   */
  getAllConnections() {
    return this.p2pManager.getAllConnections();
  }

  /**
   * Send a message to a peer
   */
  async sendMessage(peerId: string, content: MessageContent): Promise<string> {
    if (!this.isConnectedToPeer(peerId)) {
      throw new Error(`Not connected to peer ${peerId}`);
    }
    return await this.secureMessaging.sendMessage(peerId, content);
  }

  /**
   * Send a file to a peer
   */
  async sendFile(peerId: string, file: File): Promise<string> {
    if (!this.isConnectedToPeer(peerId)) {
      throw new Error(`Not connected to peer ${peerId}`);
    }
    return await this.secureMessaging.sendFile(peerId, file);
  }

  /**
   * Get device identity
   */
  getDeviceIdentity(): DeviceIdentity | null {
    return this.keyManager.getDeviceIdentity();
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    await this.stopDiscovery();
    this.p2pManager.cleanup();
    this.removeAllListeners();
    this.initialized = false;
  }

  /**
   * Setup event handlers between components
   */
  private setupEventHandlers(): void {
    // Forward P2P events
    this.p2pManager.addEventListener('connection-established', (event: any) => {
      this.emit('peer-connected', event.detail);
    });

    this.p2pManager.addEventListener('connection-closed', (event: any) => {
      this.emit('peer-disconnected', event.detail);
    });

    this.p2pManager.addEventListener(
      'connection-quality-poor',
      (event: any) => {
        this.emit('connection-quality-poor', event.detail);
      }
    );

    // Forward discovery events
    this.contactDiscovery.addEventListener(
      'contact-discovered',
      (event: any) => {
        this.emit('contact-discovered', event.detail);
      }
    );

    this.contactDiscovery.addEventListener(
      'contact-request-received',
      (event: any) => {
        this.emit('contact-request-received', event.detail);
      }
    );

    // Forward messaging events
    this.secureMessaging.addEventListener('message-received', (event: any) => {
      this.emit('message-received', event.detail);
    });

    this.secureMessaging.addEventListener('message-delivered', (event: any) => {
      this.emit('message-delivered', event.detail);
    });

    this.secureMessaging.addEventListener(
      'file-transfer-progress',
      (event: any) => {
        this.emit('file-transfer-progress', event.detail);
      }
    );

    this.secureMessaging.addEventListener('file-received', (event: any) => {
      this.emit('file-received', event.detail);
    });
  }

  /**
   * Emit an event
   */
  private emit(type: string, detail?: any): void {
    this.dispatchEvent(new CustomEvent(type, { detail }));
  }
}
