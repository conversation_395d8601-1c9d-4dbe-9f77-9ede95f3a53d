import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { P2PConnectionManager } from '../../core/p2p/P2PConnectionManager';

// Mock WebRTC APIs
const mockDataChannel = {
  readyState: 'open',
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  label: 'test',
  ordered: true,
  maxRetransmits: null,
  maxPacketLifeTime: null,
  protocol: '',
  negotiated: false,
  id: 1,
  priority: 'high',
  bufferedAmount: 0,
  bufferedAmountLowThreshold: 0,
  binaryType: 'arraybuffer' as BinaryType,
  onopen: null,
  onclose: null,
  onclosing: null,
  onerror: null,
  onmessage: null,
  onbufferedamountlow: null,
  dispatchEvent: vi.fn(),
};

const mockPeerConnection = {
  connectionState: 'new' as RTCPeerConnectionState,
  iceConnectionState: 'new' as RTCIceConnectionState,
  iceGatheringState: 'new' as RTCIceGatheringState,
  signalingState: 'stable' as RTCSignalingState,
  localDescription: null,
  remoteDescription: null,
  createOffer: vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' }),
  createAnswer: vi.fn().mockResolvedValue({ type: 'answer', sdp: 'mock-sdp' }),
  setLocalDescription: vi.fn().mockResolvedValue(undefined),
  setRemoteDescription: vi.fn().mockResolvedValue(undefined),
  addIceCandidate: vi.fn().mockResolvedValue(undefined),
  createDataChannel: vi.fn().mockReturnValue(mockDataChannel),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
  getStats: vi.fn().mockResolvedValue(
    new Map([
      [
        'candidate-pair-1',
        {
          type: 'candidate-pair',
          state: 'succeeded',
          currentRoundTripTime: 0.05, // 50ms
        },
      ],
      [
        'outbound-rtp-1',
        {
          type: 'outbound-rtp',
          packetsSent: 100,
          bytesSent: 10000,
        },
      ],
      [
        'inbound-rtp-1',
        {
          type: 'inbound-rtp',
          packetsLost: 2,
          bytesReceived: 9800,
        },
      ],
    ])
  ),
  onconnectionstatechange: null,
  oniceconnectionstatechange: null,
  onicegatheringstatechange: null,
  onsignalingstatechange: null,
  onicecandidate: null,
  ondatachannel: null,
  onnegotiationneeded: null,
  ontrack: null,
};

// Mock RTCPeerConnection constructor
const MockRTCPeerConnection = vi
  .fn()
  .mockImplementation(() => mockPeerConnection) as any;
MockRTCPeerConnection.generateCertificate = vi.fn().mockResolvedValue({});
global.RTCPeerConnection = MockRTCPeerConnection;

describe('P2PConnectionManager Integration Tests', () => {
  let connectionManager: P2PConnectionManager;

  beforeEach(() => {
    vi.clearAllMocks();
    connectionManager = new P2PConnectionManager({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      connectionTimeout: 5000,
      keepAliveInterval: 2000,
    });
  });

  afterEach(() => {
    connectionManager.cleanup();
  });

  describe('Connection Creation', () => {
    it('should create a new peer connection with proper configuration', async () => {
      const peerId = 'test-peer-1';

      const connection = await connectionManager.createConnection(peerId, true);

      expect(RTCPeerConnection).toHaveBeenCalledWith({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
        iceCandidatePoolSize: 10,
        bundlePolicy: 'max-bundle',
        rtcpMuxPolicy: 'require',
      });

      expect(connection).toBe(mockPeerConnection);
      expect(connectionManager.getConnectionInfo(peerId)).toBeDefined();
    });

    it('should create data channels for initiator', async () => {
      const peerId = 'test-peer-1';

      await connectionManager.createConnection(peerId, true);

      expect(mockPeerConnection.createDataChannel).toHaveBeenCalledWith(
        'messages',
        {
          ordered: true,
          maxRetransmits: 3,
        }
      );

      expect(mockPeerConnection.createDataChannel).toHaveBeenCalledWith(
        'files',
        {
          ordered: false,
          maxPacketLifeTime: 3000,
        }
      );
    });

    it('should set up event handlers for the connection', async () => {
      const peerId = 'test-peer-1';

      await connectionManager.createConnection(peerId, true);

      // Verify that the connection was created successfully
      const connectionInfo = connectionManager.getConnectionInfo(peerId);
      expect(connectionInfo).toBeDefined();
      expect(connectionInfo!.peerId).toBe(peerId);
      expect(connectionInfo!.state).toBe('connecting');
    });

    it('should handle connection creation errors', async () => {
      const peerId = 'test-peer-1';
      const error = new Error('Connection failed');

      vi.mocked(RTCPeerConnection).mockImplementationOnce(() => {
        throw error;
      });

      await expect(
        connectionManager.createConnection(peerId, true)
      ).rejects.toThrow('Connection failed');
    });
  });

  describe('Message Sending', () => {
    beforeEach(async () => {
      await connectionManager.createConnection('test-peer-1', true);
    });

    it('should send messages through the correct data channel', async () => {
      const message = { type: 'test', content: 'Hello World' };

      await connectionManager.sendMessage('test-peer-1', message, 'messages');

      expect(mockDataChannel.send).toHaveBeenCalledWith(
        JSON.stringify(message)
      );
    });

    it('should queue messages when data channel is not ready', async () => {
      mockDataChannel.readyState = 'connecting';
      const message = { type: 'test', content: 'Hello World' };

      await connectionManager.sendMessage('test-peer-1', message, 'messages');

      // Message should be queued, not sent immediately
      expect(mockDataChannel.send).not.toHaveBeenCalled();

      // Simulate channel opening
      mockDataChannel.readyState = 'open';
      const messageQueue = (connectionManager as any).messageQueues.get(
        'test-peer-1'
      );

      expect(messageQueue).toBeDefined();
      expect(messageQueue.length).toBe(1);
    });

    it('should handle send errors gracefully', async () => {
      mockDataChannel.send.mockImplementationOnce(() => {
        throw new Error('Send failed');
      });

      const message = { type: 'test', content: 'Hello World' };

      await expect(
        connectionManager.sendMessage('test-peer-1', message, 'messages')
      ).rejects.toThrow('Send failed');
    });
  });

  describe('Connection Quality Monitoring', () => {
    beforeEach(async () => {
      await connectionManager.createConnection('test-peer-1', true);
      // Simulate connected state
      const connectionInfo =
        connectionManager.getConnectionInfo('test-peer-1')!;
      connectionInfo.state = 'connected';
    });

    it('should track connection quality metrics', async () => {
      const quality = connectionManager.getConnectionQuality('test-peer-1');

      expect(quality).toBeDefined();
      expect(quality!.rtt).toBe(0);
      expect(quality!.packetsLost).toBe(0);
      expect(quality!.packetsSent).toBe(0);
    });

    it('should update connection statistics', async () => {
      // Trigger stats update
      await (connectionManager as any).updateConnectionStats('test-peer-1');

      const quality = connectionManager.getConnectionQuality('test-peer-1');

      expect(quality!.rtt).toBe(50); // 0.05 * 1000
      expect(quality!.packetsSent).toBe(100);
      expect(quality!.packetsLost).toBe(2);
      expect(quality!.bytesReceived).toBe(9800);
      expect(quality!.bytesSent).toBe(10000);
    });

    it('should emit quality update events', async () => {
      const qualityUpdateSpy = vi.fn();
      connectionManager.addEventListener(
        'connection-quality-update',
        qualityUpdateSpy
      );

      await (connectionManager as any).updateConnectionStats('test-peer-1');

      expect(qualityUpdateSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            peerId: 'test-peer-1',
            quality: expect.objectContaining({
              rtt: 50,
              packetsSent: 100,
              packetsLost: 2,
            }),
          },
        })
      );
    });

    it('should detect poor connection quality', async () => {
      // Mock poor quality stats
      mockPeerConnection.getStats.mockResolvedValueOnce(
        new Map([
          [
            'candidate-pair-1',
            {
              type: 'candidate-pair',
              state: 'succeeded',
              currentRoundTripTime: 2.0, // 2000ms - poor RTT
            },
          ],
          [
            'outbound-rtp-1',
            {
              type: 'outbound-rtp',
              packetsSent: 100,
              bytesSent: 10000,
            },
          ],
          [
            'inbound-rtp-1',
            {
              type: 'inbound-rtp',
              packetsLost: 20, // 20% packet loss
              bytesReceived: 8000,
            },
          ],
        ])
      );

      const poorQualitySpy = vi.fn();
      connectionManager.addEventListener(
        'connection-quality-poor',
        poorQualitySpy
      );

      await (connectionManager as any).updateConnectionStats('test-peer-1');

      expect(poorQualitySpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            peerId: 'test-peer-1',
            quality: expect.objectContaining({
              rtt: 2000,
              packetsLost: 20,
            }),
          },
        })
      );
    });
  });

  describe('Connection Cleanup', () => {
    beforeEach(async () => {
      await connectionManager.createConnection('test-peer-1', true);
      await connectionManager.createConnection('test-peer-2', true);
    });

    it('should close individual connections', async () => {
      await connectionManager.closeConnection('test-peer-1');

      expect(mockPeerConnection.close).toHaveBeenCalled();
      expect(
        connectionManager.getConnectionInfo('test-peer-1')
      ).toBeUndefined();
      expect(connectionManager.getConnectionInfo('test-peer-2')).toBeDefined();
    });

    it('should cleanup all connections', () => {
      connectionManager.cleanup();

      expect(connectionManager.getAllConnections().size).toBe(0);
    });

    it('should close data channels before closing connection', async () => {
      await connectionManager.closeConnection('test-peer-1');

      expect(mockDataChannel.close).toHaveBeenCalled();
      expect(mockPeerConnection.close).toHaveBeenCalled();
    });
  });

  describe('Connection State Management', () => {
    beforeEach(async () => {
      await connectionManager.createConnection('test-peer-1', true);
    });

    it('should track connection state correctly', () => {
      const connectionInfo =
        connectionManager.getConnectionInfo('test-peer-1')!;

      expect(connectionInfo.state).toBe('connecting');
      expect(connectionInfo.iceState).toBe('new');
      expect(connectionInfo.reconnectAttempts).toBe(0);
    });

    it('should detect connected state', () => {
      const connectionInfo =
        connectionManager.getConnectionInfo('test-peer-1')!;
      connectionInfo.state = 'connected';

      expect(connectionManager.isConnected('test-peer-1')).toBe(true);
    });

    it('should detect disconnected state', () => {
      expect(connectionManager.isConnected('test-peer-1')).toBe(false);
    });
  });
});
