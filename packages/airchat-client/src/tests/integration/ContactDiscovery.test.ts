import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ContactDiscovery } from '../../core/discovery/ContactDiscovery';
import { DiscoveryClient } from '../../core/discovery/DiscoveryClient';
import { P2PConnectionManager } from '../../core/p2p/P2PConnectionManager';
import { E2EEncryption } from '../../core/crypto/E2EEncryption';

// Mock the DiscoveryClient
vi.mock('../../core/discovery/DiscoveryClient');
vi.mock('../../core/p2p/P2PConnectionManager');

describe('ContactDiscovery Integration Tests', () => {
  let contactDiscovery: ContactDiscovery;
  let mockConnectionManager: P2PConnectionManager;
  let mockDiscoveryClient: DiscoveryClient;
  let deviceIdentity: any;

  beforeEach(async () => {
    // Create mock device identity
    const e2e = new E2EEncryption();
    deviceIdentity = await e2e.initialize();

    // Create mocks
    mockConnectionManager = {
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      broadcastMessage: vi.fn(),
      sendMessage: vi.fn(),
    } as any;
    mockDiscoveryClient = new DiscoveryClient();

    // Create ContactDiscovery instance
    contactDiscovery = new ContactDiscovery(mockConnectionManager);

    // Mock the discovery client methods
    vi.mocked(mockDiscoveryClient.registerDevice).mockResolvedValue(undefined);
    vi.mocked(mockDiscoveryClient.updatePresence).mockResolvedValue(undefined);
    vi.mocked(mockDiscoveryClient.discoverContacts).mockResolvedValue([
      {
        deviceId: 'test-device-1',
        deviceName: 'Test Device 1',
        publicKey: 'base64-encoded-key',
        lastSeen: Date.now(),
        location: { country: 'US' },
      },
      {
        deviceId: 'test-device-2',
        deviceName: 'Test Device 2',
        publicKey: 'base64-encoded-key-2',
        lastSeen: Date.now() - 60000,
        location: { country: 'CA' },
      },
    ]);
    vi.mocked(mockDiscoveryClient.sendContactRequest).mockResolvedValue(
      'request-123'
    );
    vi.mocked(mockDiscoveryClient.respondToContactRequest).mockResolvedValue(
      undefined
    );

    // Add missing mock methods
    mockDiscoveryClient.initialize = vi.fn();
    mockDiscoveryClient.convertServerContact = vi
      .fn()
      .mockImplementation(serverContact => ({
        deviceId: serverContact.deviceId,
        publicKey: new Uint8Array(32), // Mock public key
        deviceName: serverContact.deviceName,
        lastSeen: serverContact.lastSeen,
        location: serverContact.location,
      }));

    // Replace the internal discovery client
    (contactDiscovery as any).discoveryClient = mockDiscoveryClient;
  });

  afterEach(() => {
    if (contactDiscovery) {
      contactDiscovery.cleanup();
    }
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with device identity and register with server', async () => {
      await contactDiscovery.initialize(deviceIdentity);

      expect(mockDiscoveryClient.initialize).toHaveBeenCalledWith(
        deviceIdentity
      );
      expect(mockDiscoveryClient.registerDevice).toHaveBeenCalledWith(
        'AirChat User',
        true
      );
    });

    it('should emit discovery-initialized event', async () => {
      const eventSpy = vi.fn();
      contactDiscovery.addEventListener('discovery-initialized', eventSpy);

      await contactDiscovery.initialize(deviceIdentity);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { deviceId: deviceIdentity.deviceId },
        })
      );
    });

    it('should handle initialization errors', async () => {
      vi.mocked(mockDiscoveryClient.registerDevice).mockRejectedValue(
        new Error('Server error')
      );

      await expect(contactDiscovery.initialize(deviceIdentity)).rejects.toThrow(
        'Server error'
      );
    });
  });

  describe('Contact Discovery', () => {
    beforeEach(async () => {
      await contactDiscovery.initialize(deviceIdentity);
    });

    it('should discover contacts from server', async () => {
      const discoveredSpy = vi.fn();
      contactDiscovery.addEventListener('contact-discovered', discoveredSpy);

      await contactDiscovery.startDiscovery();

      expect(mockDiscoveryClient.discoverContacts).toHaveBeenCalledWith(
        50, // maxDiscoveredContacts
        [deviceIdentity.deviceId] // exclude self
      );

      expect(discoveredSpy).toHaveBeenCalledTimes(2);
      expect(discoveredSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            contact: expect.objectContaining({
              deviceId: 'test-device-1',
              deviceName: 'Test Device 1',
              discoveryMethod: 'webrtc',
              verified: false,
            }),
          },
        })
      );
    });

    it('should emit discovery-started and discovery-stopped events', async () => {
      const startedSpy = vi.fn();
      const stoppedSpy = vi.fn();
      contactDiscovery.addEventListener('discovery-started', startedSpy);
      contactDiscovery.addEventListener('discovery-stopped', stoppedSpy);

      await contactDiscovery.startDiscovery();
      contactDiscovery.stopDiscovery();

      expect(startedSpy).toHaveBeenCalled();
      expect(stoppedSpy).toHaveBeenCalled();
    });

    it('should handle discovery errors', async () => {
      vi.mocked(mockDiscoveryClient.discoverContacts).mockRejectedValue(
        new Error('Network error')
      );

      await expect(contactDiscovery.startDiscovery()).rejects.toThrow(
        'Network error'
      );
      expect(contactDiscovery.getIsDiscovering()).toBe(false);
    });

    it('should return discovered contacts', async () => {
      await contactDiscovery.startDiscovery();

      const contacts = contactDiscovery.getDiscoveredContacts();
      expect(contacts).toHaveLength(2);
      expect(contacts[0]).toMatchObject({
        deviceId: 'test-device-1',
        deviceName: 'Test Device 1',
      });
    });
  });

  describe('Contact Requests', () => {
    beforeEach(async () => {
      await contactDiscovery.initialize(deviceIdentity);
      await contactDiscovery.startDiscovery();
    });

    it('should send contact request through server', async () => {
      const requestSpy = vi.fn();
      contactDiscovery.addEventListener('contact-request-sent', requestSpy);

      const requestId = await contactDiscovery.sendContactRequest(
        'test-device-1',
        'Hello!'
      );

      expect(mockDiscoveryClient.sendContactRequest).toHaveBeenCalledWith(
        'test-device-1',
        'Hello!'
      );
      expect(requestId).toBe('request-123');
      expect(requestSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            request: expect.objectContaining({
              id: 'request-123',
              to: 'test-device-1',
              message: 'Hello!',
            }),
            targetDeviceId: 'test-device-1',
          },
        })
      );
    });

    it('should respond to contact request through server', async () => {
      // Add a mock contact request
      const mockRequest = {
        id: 'request-456',
        from: 'test-device-1',
        to: deviceIdentity.deviceId,
        fromDeviceId: 'test-device-1',
        fromPublicKey: new Uint8Array(32),
        fromDeviceName: 'Test Device 1',
        message: 'Hi there!',
        timestamp: Date.now(),
        status: 'pending' as const,
      };
      contactDiscovery.getContactRequests().set('request-456', mockRequest);

      const responseSpy = vi.fn();
      contactDiscovery.addEventListener(
        'contact-request-responded',
        responseSpy
      );

      await contactDiscovery.respondToContactRequest('request-456', true);

      expect(mockDiscoveryClient.respondToContactRequest).toHaveBeenCalledWith(
        'request-456',
        true
      );
      expect(responseSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            request: expect.objectContaining({
              id: 'request-456',
              status: 'accepted',
            }),
            accepted: true,
          },
        })
      );
    });

    it('should handle contact request errors', async () => {
      vi.mocked(mockDiscoveryClient.sendContactRequest).mockRejectedValue(
        new Error('Request failed')
      );

      await expect(
        contactDiscovery.sendContactRequest('test-device-1', 'Hello!')
      ).rejects.toThrow('Request failed');
    });

    it('should throw error for unknown contact request', async () => {
      await expect(
        contactDiscovery.respondToContactRequest('unknown-request', true)
      ).rejects.toThrow('Contact request not found');
    });
  });

  describe('Presence Updates', () => {
    beforeEach(async () => {
      await contactDiscovery.initialize(deviceIdentity);
    });

    it('should start presence updates after initialization', () => {
      // Verify that presence updates are started (timer is set)
      expect((contactDiscovery as any).presenceTimer).toBeTruthy();
    });

    it('should stop presence updates on cleanup', () => {
      contactDiscovery.cleanup();
      expect((contactDiscovery as any).presenceTimer).toBeNull();
    });
  });

  describe('Cleanup', () => {
    beforeEach(async () => {
      await contactDiscovery.initialize(deviceIdentity);
      await contactDiscovery.startDiscovery();
    });

    it('should clean up all resources', () => {
      contactDiscovery.cleanup();

      expect(contactDiscovery.getIsDiscovering()).toBe(false);
      expect(contactDiscovery.getDiscoveredContacts()).toHaveLength(0);
      expect(contactDiscovery.getContactRequests().size).toBe(0);
      expect((contactDiscovery as any).presenceTimer).toBeNull();
    });
  });
});
