import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AirChatClient } from '../../AirChatClient';
import { DeviceIdentity } from '../../core/crypto/KeyManager';

// Mock WebRTC and crypto APIs
vi.mock('../../core/p2p/P2PConnectionManager', () => ({
  P2PConnectionManager: vi.fn().mockImplementation(() => ({
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    createConnection: vi.fn(),
    isConnected: vi.fn(),
    getConnectionQuality: vi.fn(),
    getAllConnections: vi.fn(),
    cleanup: vi.fn(),
  })),
}));

vi.mock('../../core/discovery/ContactDiscovery', () => ({
  ContactDiscovery: vi.fn().mockImplementation(() => ({
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    initialize: vi.fn(),
    startDiscovery: vi.fn(),
    stopDiscovery: vi.fn(),
    getDiscoveredContacts: vi.fn(),
    sendContactRequest: vi.fn(),
    acceptContactRequest: vi.fn(),
    rejectContactRequest: vi.fn(),
  })),
}));

vi.mock('../../core/messaging/SecureMessaging', () => ({
  SecureMessaging: vi.fn().mockImplementation(() => ({
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    initialize: vi.fn(),
    sendMessage: vi.fn(),
    sendFile: vi.fn(),
  })),
}));

describe('AirChat Full Workflow E2E Tests', () => {
  let client1: AirChatClient;
  let client2: AirChatClient;
  let mockDeviceIdentity1: DeviceIdentity;
  let mockDeviceIdentity2: DeviceIdentity;

  beforeEach(async () => {
    // Create mock device identities
    mockDeviceIdentity1 = {
      deviceId: 'device-1',
      publicKey: new Uint8Array(32).fill(1),
      privateKey: new Uint8Array(32).fill(1),
      keyPair: {} as CryptoKeyPair,
    };

    mockDeviceIdentity2 = {
      deviceId: 'device-2',
      publicKey: new Uint8Array(32).fill(2),
      privateKey: new Uint8Array(32).fill(2),
      keyPair: {} as CryptoKeyPair,
    };

    // Initialize clients
    client1 = new AirChatClient();
    client2 = new AirChatClient();

    // Mock the initialization
    vi.spyOn(client1, 'initialize').mockResolvedValue(undefined);
    vi.spyOn(client2, 'initialize').mockResolvedValue(undefined);

    await client1.initialize();
    await client2.initialize();
  });

  afterEach(async () => {
    await client1.cleanup();
    await client2.cleanup();
  });

  describe('Device Setup and Identity Management', () => {
    it('should initialize device identity on first run', async () => {
      const client = new AirChatClient();

      // Mock key generation
      const mockKeyManager = {
        generateDeviceIdentity: vi.fn().mockResolvedValue(mockDeviceIdentity1),
        getDeviceIdentity: vi.fn().mockReturnValue(mockDeviceIdentity1),
        storeDeviceIdentity: vi.fn().mockResolvedValue(undefined),
      };

      // Replace the key manager
      (client as any).keyManager = mockKeyManager;

      await client.initialize();

      expect(mockKeyManager.generateDeviceIdentity).toHaveBeenCalled();
      expect(mockKeyManager.storeDeviceIdentity).toHaveBeenCalledWith(
        mockDeviceIdentity1
      );
    });

    it('should load existing device identity on subsequent runs', async () => {
      const client = new AirChatClient();

      const mockKeyManager = {
        loadDeviceIdentity: vi.fn().mockResolvedValue(mockDeviceIdentity1),
        getDeviceIdentity: vi.fn().mockReturnValue(mockDeviceIdentity1),
      };

      (client as any).keyManager = mockKeyManager;

      await client.initialize();

      expect(mockKeyManager.loadDeviceIdentity).toHaveBeenCalled();
    });

    it('should handle device identity corruption gracefully', async () => {
      const client = new AirChatClient();

      const mockKeyManager = {
        loadDeviceIdentity: vi
          .fn()
          .mockRejectedValue(new Error('Corrupted identity')),
        generateDeviceIdentity: vi.fn().mockResolvedValue(mockDeviceIdentity1),
        getDeviceIdentity: vi.fn().mockReturnValue(mockDeviceIdentity1),
        storeDeviceIdentity: vi.fn().mockResolvedValue(undefined),
      };

      (client as any).keyManager = mockKeyManager;

      await client.initialize();

      expect(mockKeyManager.loadDeviceIdentity).toHaveBeenCalled();
      expect(mockKeyManager.generateDeviceIdentity).toHaveBeenCalled();
    });
  });

  describe('Contact Discovery Workflow', () => {
    it('should discover nearby contacts', async () => {
      const mockContactDiscovery = {
        startDiscovery: vi.fn().mockResolvedValue(undefined),
        stopDiscovery: vi.fn().mockResolvedValue(undefined),
        getDiscoveredContacts: vi.fn().mockReturnValue(
          new Map([
            [
              'device-2',
              {
                deviceId: 'device-2',
                publicKey: mockDeviceIdentity2.publicKey,
                deviceName: 'Test Device 2',
                lastSeen: Date.now(),
                discoveryMethod: 'webrtc',
                verified: false,
              },
            ],
          ])
        ),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).contactDiscovery = mockContactDiscovery;

      await client1.startDiscovery();

      expect(mockContactDiscovery.startDiscovery).toHaveBeenCalled();

      const discoveredContacts = client1.getDiscoveredContacts();
      expect(discoveredContacts.size).toBe(1);
      expect(discoveredContacts.get('device-2')).toBeDefined();
    });

    it('should handle discovery errors gracefully', async () => {
      const mockContactDiscovery = {
        startDiscovery: vi
          .fn()
          .mockRejectedValue(new Error('Discovery failed')),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).contactDiscovery = mockContactDiscovery;

      await expect(client1.startDiscovery()).rejects.toThrow(
        'Discovery failed'
      );
    });

    it('should send and receive contact requests', async () => {
      const mockContactDiscovery = {
        sendContactRequest: vi.fn().mockResolvedValue('request-123'),
        acceptContactRequest: vi.fn().mockResolvedValue(undefined),
        rejectContactRequest: vi.fn().mockResolvedValue(undefined),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).contactDiscovery = mockContactDiscovery;

      const requestId = await client1.sendContactRequest(
        'device-2',
        'Hello from Device 1'
      );
      expect(requestId).toBe('request-123');
      expect(mockContactDiscovery.sendContactRequest).toHaveBeenCalledWith(
        'device-2',
        'Hello from Device 1'
      );

      await client1.acceptContactRequest('request-123');
      expect(mockContactDiscovery.acceptContactRequest).toHaveBeenCalledWith(
        'request-123'
      );
    });
  });

  describe('P2P Connection Establishment', () => {
    it('should establish P2P connection between devices', async () => {
      const mockP2PManager = {
        createConnection: vi.fn().mockResolvedValue({}),
        isConnected: vi.fn().mockReturnValue(true),
        sendMessage: vi.fn().mockResolvedValue(undefined),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      await client1.connectToPeer('device-2');

      expect(mockP2PManager.createConnection).toHaveBeenCalledWith(
        'device-2',
        true
      );
      expect(client1.isConnectedToPeer('device-2')).toBe(true);
    });

    it('should handle connection failures with retry logic', async () => {
      const mockP2PManager = {
        createConnection: vi
          .fn()
          .mockRejectedValueOnce(new Error('Connection failed'))
          .mockRejectedValueOnce(new Error('Connection failed'))
          .mockResolvedValue({}),
        isConnected: vi.fn().mockReturnValue(false),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      // Should retry and eventually succeed
      await client1.connectToPeer('device-2');

      expect(mockP2PManager.createConnection).toHaveBeenCalledTimes(3);
    });

    it('should monitor connection quality', async () => {
      const mockP2PManager = {
        getConnectionQuality: vi.fn().mockReturnValue({
          rtt: 50,
          packetsLost: 0,
          packetsSent: 100,
          bytesReceived: 10000,
          bytesSent: 10000,
          lastStatsUpdate: Date.now(),
        }),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      const quality = client1.getConnectionQuality('device-2');

      expect(quality).toBeDefined();
      expect(quality!.rtt).toBe(50);
      expect(quality!.packetsLost).toBe(0);
    });
  });

  describe('Secure Messaging Workflow', () => {
    beforeEach(() => {
      // Mock connected state
      const mockP2PManager = {
        isConnected: vi.fn().mockReturnValue(true),
        sendMessage: vi.fn().mockResolvedValue(undefined),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;
      (client2 as any).p2pManager = mockP2PManager;
    });

    it('should send and receive text messages', async () => {
      const mockSecureMessaging = {
        sendMessage: vi.fn().mockResolvedValue('message-123'),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).secureMessaging = mockSecureMessaging;

      const messageId = await client1.sendMessage('device-2', {
        type: 'text',
        text: 'Hello, World!',
      });

      expect(messageId).toBe('message-123');
      expect(mockSecureMessaging.sendMessage).toHaveBeenCalledWith(
        'device-2',
        expect.objectContaining({
          type: 'text',
          text: 'Hello, World!',
        })
      );
    });

    it('should handle message encryption and decryption', async () => {
      const mockSecureMessaging = {
        sendMessage: vi.fn().mockImplementation(async (peerId, content) => {
          // Simulate encryption
          const encrypted = btoa(JSON.stringify(content));
          return `encrypted-${encrypted}`;
        }),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).secureMessaging = mockSecureMessaging;

      const messageId = await client1.sendMessage('device-2', {
        type: 'text',
        text: 'Secret message',
      });

      expect(messageId).toContain('encrypted-');
    });

    it('should support file sharing', async () => {
      const mockFile = new File(['test content'], 'test.txt', {
        type: 'text/plain',
      });

      const mockSecureMessaging = {
        sendFile: vi.fn().mockResolvedValue('file-transfer-123'),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).secureMessaging = mockSecureMessaging;

      const transferId = await client1.sendFile('device-2', mockFile);

      expect(transferId).toBe('file-transfer-123');
      expect(mockSecureMessaging.sendFile).toHaveBeenCalledWith(
        'device-2',
        mockFile
      );
    });

    it('should handle message delivery confirmation', async () => {
      const mockSecureMessaging = {
        sendMessage: vi.fn().mockResolvedValue('message-123'),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).secureMessaging = mockSecureMessaging;

      const messageId = await client1.sendMessage('device-2', {
        type: 'text',
        text: 'Test message',
      });

      // Simulate delivery confirmation
      const deliveryEvent = new CustomEvent('message-delivered', {
        detail: { messageId, peerId: 'device-2' },
      });

      (client1 as any).secureMessaging.dispatchEvent(deliveryEvent);

      expect(messageId).toBe('message-123');
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle network disconnection gracefully', async () => {
      const mockP2PManager = {
        isConnected: vi.fn().mockReturnValue(false),
        createConnection: vi
          .fn()
          .mockRejectedValue(new Error('Network unavailable')),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      await expect(client1.connectToPeer('device-2')).rejects.toThrow(
        'Network unavailable'
      );
    });

    it('should recover from temporary connection loss', async () => {
      const mockP2PManager = {
        isConnected: vi
          .fn()
          .mockReturnValueOnce(true)
          .mockReturnValueOnce(false)
          .mockReturnValue(true),
        createConnection: vi.fn().mockResolvedValue({}),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      // Simulate connection recovery
      expect(client1.isConnectedToPeer('device-2')).toBe(true);
      expect(client1.isConnectedToPeer('device-2')).toBe(false);

      await client1.connectToPeer('device-2');
      expect(client1.isConnectedToPeer('device-2')).toBe(true);
    });

    it('should handle storage quota exceeded', async () => {
      const mockKeyManager = {
        storeDeviceIdentity: vi
          .fn()
          .mockRejectedValue(new Error('QuotaExceededError')),
        generateDeviceIdentity: vi.fn().mockResolvedValue(mockDeviceIdentity1),
        getDeviceIdentity: vi.fn().mockReturnValue(mockDeviceIdentity1),
      };

      const client = new AirChatClient();
      (client as any).keyManager = mockKeyManager;

      await expect(client.initialize()).rejects.toThrow('QuotaExceededError');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent connections', async () => {
      const mockP2PManager = {
        createConnection: vi.fn().mockResolvedValue({}),
        isConnected: vi.fn().mockReturnValue(true),
        getAllConnections: vi.fn().mockReturnValue(
          new Map([
            ['device-2', {}],
            ['device-3', {}],
            ['device-4', {}],
          ])
        ),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).p2pManager = mockP2PManager;

      await Promise.all([
        client1.connectToPeer('device-2'),
        client1.connectToPeer('device-3'),
        client1.connectToPeer('device-4'),
      ]);

      const connections = client1.getAllConnections();
      expect(connections.size).toBe(3);
    });

    it('should handle high message throughput', async () => {
      const mockSecureMessaging = {
        sendMessage: vi.fn().mockResolvedValue('message-id'),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      (client1 as any).secureMessaging = mockSecureMessaging;

      const messages = Array.from({ length: 100 }, (_, i) => ({
        type: 'text' as const,
        text: `Message ${i}`,
      }));

      await Promise.all(
        messages.map(message => client1.sendMessage('device-2', message))
      );

      expect(mockSecureMessaging.sendMessage).toHaveBeenCalledTimes(100);
    });
  });
});
