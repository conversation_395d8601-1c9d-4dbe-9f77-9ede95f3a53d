import { useEffect, useState } from 'react';
import { AppProvider } from './contexts/AppContext';
import { DeviceSetup } from './components/setup/DeviceSetup';
import { MainInterface } from './components/layout/MainInterface';
import { LoadingScreen } from './components/common/LoadingScreen';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { useAppStore } from './stores/appStore';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const { isInitialized, deviceIdentity, initializeApp } = useAppStore();

  useEffect(() => {
    const init = async () => {
      try {
        await initializeApp();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      } finally {
        setIsLoading(false);
      }
    };

    init();
  }, [initializeApp]);

  if (isLoading) {
    return <LoadingScreen message="Initializing AirChat..." />;
  }

  return (
    <ErrorBoundary>
      <AppProvider>
        <div className="app">
          {!isInitialized || !deviceIdentity ? (
            <DeviceSetup />
          ) : (
            <MainInterface />
          )}
        </div>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
