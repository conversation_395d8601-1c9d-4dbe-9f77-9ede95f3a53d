{"extends": "../../tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/core/*": ["src/core/*"], "@/hooks/*": ["src/hooks/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/styles/*": ["src/styles/*"], "@airchat/shared": ["../shared/src/index.ts"]}}, "include": ["src", "tests", "vite.config.ts", "vitest.config.ts", "playwright.config.ts"], "exclude": ["node_modules", "dist"]}