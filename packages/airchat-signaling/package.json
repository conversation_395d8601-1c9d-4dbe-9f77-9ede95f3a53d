{"name": "@airchat/signaling", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "build": "tsc", "test": "vitest run", "test:watch": "vitest", "type-check": "tsc --noEmit", "lint": "eslint src --ext ts --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@airchat/shared": "workspace:*"}, "devDependencies": {"@cloudflare/workers-types": "^4.20230821.0", "@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "typescript": "^5.0.2", "vitest": "^3.2.4", "wrangler": "^3.6.0"}}