import { SignalingRoom } from './SignalingRoom';

export interface Env {
  SIGNALING_ROOM: DurableObjectNamespace;
  ENVIRONMENT: string;
}

export { SignalingRoom };

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return handleCORS(request);
    }

    // Health check endpoint
    if (url.pathname === '/health') {
      return new Response(JSON.stringify({
        status: 'healthy',
        timestamp: Date.now(),
        environment: env.ENVIRONMENT
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...getCORSHeaders()
        }
      });
    }

    // WebSocket endpoint
    if (url.pathname === '/websocket') {
      // Get or create a Durable Object instance
      // Use a single room for simplicity, but could be room-specific
      const roomId = url.searchParams.get('room') || 'default';
      const id = env.SIGNALING_ROOM.idFromName(roomId);
      const room = env.SIGNALING_ROOM.get(id);
      
      return room.fetch(request);
    }

    // API endpoints
    if (url.pathname.startsWith('/api/')) {
      return handleAPI(request, env, ctx);
    }

    return new Response('Not found', { 
      status: 404,
      headers: getCORSHeaders()
    });
  },

  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    // Cleanup inactive sessions every 5 minutes
    console.log('Running scheduled cleanup');
    
    // Get all room instances and trigger cleanup
    // This is a simplified approach - in production you'd want to track active rooms
    const roomId = 'default';
    const id = env.SIGNALING_ROOM.idFromName(roomId);
    const room = env.SIGNALING_ROOM.get(id);
    
    try {
      await room.fetch(new Request('https://dummy.com/cleanup', { method: 'POST' }));
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }
};

async function handleAPI(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname.replace('/api', '');

  switch (path) {
    case '/rooms':
      return handleRoomsAPI(request, env);
    
    case '/stats':
      return handleStatsAPI(request, env);
    
    default:
      return new Response('API endpoint not found', { 
        status: 404,
        headers: getCORSHeaders()
      });
  }
}

async function handleRoomsAPI(request: Request, env: Env): Promise<Response> {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { 
      status: 405,
      headers: getCORSHeaders()
    });
  }

  // This is a simplified implementation
  // In a real app, you'd query the Durable Objects for room information
  const rooms = [
    {
      id: 'default',
      participants: 0,
      createdAt: Date.now(),
      lastActivity: Date.now()
    }
  ];

  return new Response(JSON.stringify({ rooms }), {
    headers: {
      'Content-Type': 'application/json',
      ...getCORSHeaders()
    }
  });
}

async function handleStatsAPI(request: Request, env: Env): Promise<Response> {
  if (request.method !== 'GET') {
    return new Response('Method not allowed', { 
      status: 405,
      headers: getCORSHeaders()
    });
  }

  const stats = {
    totalRooms: 1,
    totalConnections: 0,
    uptime: Date.now(),
    version: '1.0.0'
  };

  return new Response(JSON.stringify(stats), {
    headers: {
      'Content-Type': 'application/json',
      ...getCORSHeaders()
    }
  });
}

function handleCORS(request: Request): Response {
  return new Response(null, {
    status: 200,
    headers: getCORSHeaders()
  });
}

function getCORSHeaders(): Record<string, string> {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400'
  };
}
