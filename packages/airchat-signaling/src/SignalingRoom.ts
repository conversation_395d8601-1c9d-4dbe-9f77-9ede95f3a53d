import type {
  AnySignalingMessage,
  Room,
  Peer,
  SignalingError,
} from '@airchat/shared';

export interface WebSocketSession {
  webSocket: WebSocket;
  peerId: string;
  rooms: Set<string>;
  lastSeen: number;
  status: 'online' | 'offline' | 'away';
  deviceRegistration?: DeviceRegistration;
}

export interface DeviceRegistration {
  deviceId: string;
  publicKey: string; // Base64 encoded public key
  deviceName: string;
  lastSeen: number;
  isOnline: boolean;
  discoverable: boolean;
  location?: {
    country?: string;
    region?: string;
  };
  connectionInfo?: {
    userAgent?: string;
    ipHash?: string; // Hashed IP for privacy
  };
}

export interface ContactDiscoveryRequest {
  deviceId: string;
  maxResults?: number;
  location?: {
    country?: string;
    region?: string;
  };
  excludeDeviceIds?: string[];
}

export interface ContactRequest {
  id: string;
  fromDeviceId: string;
  toDeviceId: string;
  fromDeviceName?: string;
  message?: string;
  timestamp: number;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  expiresAt: number;
}

export class SignalingRoom implements DurableObject {
  private state: DurableObjectState;
  private sessions = new Map<string, WebSocketSession>();
  private rooms = new Map<string, Room>();
  private peers = new Map<string, Peer>();
  private deviceRegistrations = new Map<string, DeviceRegistration>();
  private contactRequests = new Map<string, ContactRequest>();
  private rateLimiter = new Map<string, { count: number; resetTime: number }>();

  constructor(state: DurableObjectState, env: any) {
    this.state = state;
    this.loadPersistedData();
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);

    // Enable CORS for all requests
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    if (url.pathname === '/websocket') {
      return this.handleWebSocket(request);
    }

    if (url.pathname === '/health') {
      return new Response('OK', { status: 200, headers: corsHeaders });
    }

    // Contact Discovery API endpoints
    if (url.pathname === '/api/register-device' && request.method === 'POST') {
      return this.handleDeviceRegistration(request, corsHeaders);
    }

    if (
      url.pathname === '/api/discover-contacts' &&
      request.method === 'POST'
    ) {
      return this.handleContactDiscovery(request, corsHeaders);
    }

    if (url.pathname === '/api/contact-request' && request.method === 'POST') {
      return this.handleContactRequest(request, corsHeaders);
    }

    if (url.pathname === '/api/contact-response' && request.method === 'POST') {
      return this.handleContactResponse(request, corsHeaders);
    }

    if (url.pathname === '/api/update-presence' && request.method === 'POST') {
      return this.handlePresenceUpdateHTTP(request, corsHeaders);
    }

    return new Response('Not found', { status: 404, headers: corsHeaders });
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 400 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    server.accept();

    // Handle new connection
    server.addEventListener('message', event => {
      this.handleMessage(server, event.data);
    });

    server.addEventListener('close', event => {
      this.handleDisconnection(server, event.code, event.reason);
    });

    server.addEventListener('error', event => {
      console.error('WebSocket error:', event);
      this.handleDisconnection(server, 1011, 'Internal error');
    });

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleMessage(
    webSocket: WebSocket,
    data: string
  ): Promise<void> {
    try {
      const message = JSON.parse(data) as
        | AnySignalingMessage
        | { type: 'ping' };

      // Handle ping/pong for heartbeat
      if (message.type === 'ping') {
        webSocket.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
        this.updateLastSeen(webSocket);
        return;
      }

      const signalingMessage = message as AnySignalingMessage;

      // Ensure session exists
      let session = this.getSessionByWebSocket(webSocket);
      if (!session && signalingMessage.type !== 'join-room') {
        this.sendError(webSocket, {
          code: 'NO_SESSION',
          message: 'Must join a room first',
        });
        return;
      }

      switch (signalingMessage.type) {
        case 'join-room':
          await this.handleJoinRoom(webSocket, signalingMessage);
          break;

        case 'leave-room':
          await this.handleLeaveRoom(webSocket, signalingMessage);
          break;

        case 'offer':
        case 'answer':
        case 'ice-candidate':
          await this.handleWebRTCMessage(webSocket, signalingMessage);
          break;

        case 'presence-update':
          await this.handlePresenceUpdate(webSocket, signalingMessage);
          break;

        default:
          this.sendError(webSocket, {
            code: 'UNKNOWN_MESSAGE_TYPE',
            message: `Unknown message type: ${(signalingMessage as any).type}`,
          });
      }

      this.updateLastSeen(webSocket);
    } catch (error) {
      console.error('Error handling message:', error);
      this.sendError(webSocket, {
        code: 'MESSAGE_PARSE_ERROR',
        message: 'Failed to parse message',
        details: error,
      });
    }
  }

  private async handleJoinRoom(
    webSocket: WebSocket,
    message: Extract<AnySignalingMessage, { type: 'join-room' }>
  ): Promise<void> {
    const { from: peerId, room: roomId } = message;

    if (!peerId || !roomId) {
      this.sendError(webSocket, {
        code: 'INVALID_JOIN_REQUEST',
        message: 'Missing peerId or roomId',
      });
      return;
    }

    // Create or get existing session
    let session = this.getSessionByWebSocket(webSocket);
    if (!session) {
      session = {
        webSocket,
        peerId,
        rooms: new Set(),
        lastSeen: Date.now(),
        status: 'online',
      };
      this.sessions.set(peerId, session);
    }

    // Create or get room
    let room = this.rooms.get(roomId);
    if (!room) {
      room = {
        id: roomId,
        participants: new Set(),
        createdAt: Date.now(),
        lastActivity: Date.now(),
      };
      this.rooms.set(roomId, room);
    }

    // Add peer to room
    room.participants.add(peerId);
    session.rooms.add(roomId);
    room.lastActivity = Date.now();

    // Update peer info
    const peer: Peer = {
      id: peerId,
      status: 'online',
      lastSeen: Date.now(),
      rooms: new Set([roomId]),
    };
    this.peers.set(peerId, peer);

    // Notify other participants
    const joinMessage = {
      type: 'peer-joined',
      peerId,
      room: roomId,
      from: 'server',
      timestamp: Date.now(),
    };

    await this.broadcastToRoom(roomId, joinMessage, peerId);

    // Send confirmation to joining peer
    webSocket.send(
      JSON.stringify({
        type: 'room-joined',
        room: roomId,
        participants: Array.from(room.participants),
        timestamp: Date.now(),
      })
    );

    console.log(`Peer ${peerId} joined room ${roomId}`);
  }

  private async handleLeaveRoom(
    webSocket: WebSocket,
    message: Extract<AnySignalingMessage, { type: 'leave-room' }>
  ): Promise<void> {
    const { from: peerId, room: roomId } = message;

    const session = this.getSessionByWebSocket(webSocket);
    if (!session || !roomId) return;

    await this.removeFromRoom(peerId, roomId);

    webSocket.send(
      JSON.stringify({
        type: 'room-left',
        room: roomId,
        timestamp: Date.now(),
      })
    );
  }

  private async handleWebRTCMessage(
    webSocket: WebSocket,
    message: Extract<
      AnySignalingMessage,
      { type: 'offer' | 'answer' | 'ice-candidate' }
    >
  ): Promise<void> {
    const { to } = message;

    if (!to) {
      this.sendError(webSocket, {
        code: 'MISSING_TARGET',
        message: 'WebRTC message must specify target peer',
      });
      return;
    }

    const targetSession = this.sessions.get(to);
    if (!targetSession) {
      this.sendError(webSocket, {
        code: 'PEER_NOT_FOUND',
        message: `Target peer ${to} not found`,
      });
      return;
    }

    // Forward message to target peer
    try {
      targetSession.webSocket.send(JSON.stringify(message));
    } catch (error) {
      console.error('Failed to forward WebRTC message:', error);
      this.sendError(webSocket, {
        code: 'FORWARD_ERROR',
        message: 'Failed to forward message to target peer',
      });
    }
  }

  private async handlePresenceUpdate(
    webSocket: WebSocket,
    message: Extract<AnySignalingMessage, { type: 'presence-update' }>
  ): Promise<void> {
    const { from: peerId, status, lastSeen } = message;

    const session = this.getSessionByWebSocket(webSocket);
    if (!session) return;

    session.status = status;
    session.lastSeen = lastSeen || Date.now();

    const peer = this.peers.get(peerId);
    if (peer) {
      peer.status = status;
      peer.lastSeen = session.lastSeen;
    }

    // Broadcast presence update to all rooms the peer is in
    for (const roomId of session.rooms) {
      await this.broadcastToRoom(roomId, message, peerId);
    }
  }

  private async handleDisconnection(
    webSocket: WebSocket,
    code: number,
    reason: string
  ): Promise<void> {
    const session = this.getSessionByWebSocket(webSocket);
    if (!session) return;

    console.log(`Peer ${session.peerId} disconnected: ${code} ${reason}`);

    // Remove from all rooms
    for (const roomId of session.rooms) {
      await this.removeFromRoom(session.peerId, roomId);
    }

    // Update peer status
    const peer = this.peers.get(session.peerId);
    if (peer) {
      peer.status = 'offline';
      peer.lastSeen = Date.now();
    }

    // Remove session
    this.sessions.delete(session.peerId);
  }

  private async removeFromRoom(peerId: string, roomId: string): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) return;

    room.participants.delete(peerId);
    room.lastActivity = Date.now();

    // Notify other participants
    const leaveMessage = {
      type: 'peer-left',
      peerId,
      room: roomId,
      from: 'server',
      timestamp: Date.now(),
    };

    await this.broadcastToRoom(roomId, leaveMessage, peerId);

    // Clean up empty room
    if (room.participants.size === 0) {
      this.rooms.delete(roomId);
      console.log(`Removed empty room ${roomId}`);
    }

    // Update session
    const session = this.sessions.get(peerId);
    if (session) {
      session.rooms.delete(roomId);
    }

    // Update peer
    const peer = this.peers.get(peerId);
    if (peer) {
      peer.rooms.delete(roomId);
    }
  }

  private async broadcastToRoom(
    roomId: string,
    message: any,
    excludePeerId?: string
  ): Promise<void> {
    const room = this.rooms.get(roomId);
    if (!room) return;

    const messageStr = JSON.stringify(message);
    const promises: Promise<void>[] = [];

    for (const participantId of room.participants) {
      if (participantId === excludePeerId) continue;

      const session = this.sessions.get(participantId);
      if (session) {
        promises.push(
          new Promise<void>(resolve => {
            try {
              session.webSocket.send(messageStr);
            } catch (error) {
              console.error(`Failed to send to ${participantId}:`, error);
            }
            resolve();
          })
        );
      }
    }

    await Promise.all(promises);
  }

  private getSessionByWebSocket(
    webSocket: WebSocket
  ): WebSocketSession | undefined {
    for (const session of this.sessions.values()) {
      if (session.webSocket === webSocket) {
        return session;
      }
    }
    return undefined;
  }

  private updateLastSeen(webSocket: WebSocket): void {
    const session = this.getSessionByWebSocket(webSocket);
    if (session) {
      session.lastSeen = Date.now();
    }
  }

  private sendError(webSocket: WebSocket, error: SignalingError): void {
    try {
      webSocket.send(
        JSON.stringify({
          type: 'error',
          error,
          timestamp: Date.now(),
        })
      );
    } catch (e) {
      console.error('Failed to send error message:', e);
    }
  }

  // Cleanup inactive sessions periodically
  async cleanup(): Promise<void> {
    const now = Date.now();
    const timeout = 5 * 60 * 1000; // 5 minutes

    for (const [peerId, session] of this.sessions.entries()) {
      if (now - session.lastSeen > timeout) {
        console.log(`Cleaning up inactive session: ${peerId}`);
        await this.handleDisconnection(session.webSocket, 1000, 'Timeout');
      }
    }
  }

  // Contact Discovery API Methods

  private async loadPersistedData(): Promise<void> {
    try {
      const deviceData = await this.state.storage.get('deviceRegistrations');
      if (deviceData) {
        this.deviceRegistrations = new Map(
          Object.entries(deviceData as Record<string, DeviceRegistration>)
        );
      }

      const requestData = await this.state.storage.get('contactRequests');
      if (requestData) {
        this.contactRequests = new Map(
          Object.entries(requestData as Record<string, ContactRequest>)
        );
      }
    } catch (error) {
      console.error('Failed to load persisted data:', error);
    }
  }

  private async persistData(): Promise<void> {
    try {
      await this.state.storage.put(
        'deviceRegistrations',
        Object.fromEntries(this.deviceRegistrations)
      );
      await this.state.storage.put(
        'contactRequests',
        Object.fromEntries(this.contactRequests)
      );
    } catch (error) {
      console.error('Failed to persist data:', error);
    }
  }

  private checkRateLimit(
    identifier: string,
    maxRequests: number = 10,
    windowMs: number = 60000
  ): boolean {
    const now = Date.now();
    const limit = this.rateLimiter.get(identifier);

    if (!limit || now > limit.resetTime) {
      this.rateLimiter.set(identifier, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (limit.count >= maxRequests) {
      return false;
    }

    limit.count++;
    return true;
  }

  private async hashString(input: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(input);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async handleDeviceRegistration(
    request: Request,
    corsHeaders: Record<string, string>
  ): Promise<Response> {
    try {
      const body = await request.json();
      const { deviceId, publicKey, deviceName, discoverable = true } = body;

      if (!deviceId || !publicKey || !deviceName) {
        return new Response(
          JSON.stringify({ error: 'Missing required fields' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Rate limiting
      if (!this.checkRateLimit(`register:${deviceId}`, 5, 300000)) {
        // 5 requests per 5 minutes
        return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), {
          status: 429,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Get client info for location/connection data
      const userAgent = request.headers.get('User-Agent') || '';
      const cfConnectingIp = request.headers.get('CF-Connecting-IP') || '';
      const cfIpCountry = request.headers.get('CF-IPCountry') || '';

      // Hash IP for privacy
      const ipHash = cfConnectingIp
        ? await this.hashString(cfConnectingIp)
        : undefined;

      const registration: DeviceRegistration = {
        deviceId,
        publicKey,
        deviceName,
        lastSeen: Date.now(),
        isOnline: true,
        discoverable,
        location: cfIpCountry ? { country: cfIpCountry } : undefined,
        connectionInfo: {
          userAgent,
          ipHash,
        },
      };

      this.deviceRegistrations.set(deviceId, registration);
      await this.persistData();

      return new Response(JSON.stringify({ success: true, deviceId }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Device registration error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  }

  private async handleContactDiscovery(
    request: Request,
    corsHeaders: Record<string, string>
  ): Promise<Response> {
    try {
      const body = await request.json();
      const {
        deviceId,
        maxResults = 20,
        location,
        excludeDeviceIds = [],
      } = body;

      if (!deviceId) {
        return new Response(JSON.stringify({ error: 'Device ID required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Rate limiting
      if (!this.checkRateLimit(`discover:${deviceId}`, 20, 60000)) {
        // 20 requests per minute
        return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), {
          status: 429,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      const now = Date.now();
      const fiveMinutesAgo = now - 5 * 60 * 1000;
      const discoveredContacts: any[] = [];

      for (const [id, registration] of this.deviceRegistrations) {
        // Skip own device and excluded devices
        if (id === deviceId || excludeDeviceIds.includes(id)) continue;

        // Skip non-discoverable devices
        if (!registration.discoverable) continue;

        // Skip offline devices (not seen in last 5 minutes)
        if (registration.lastSeen < fiveMinutesAgo) continue;

        // Location filtering (if specified)
        if (location && registration.location) {
          if (
            location.country &&
            registration.location.country !== location.country
          )
            continue;
          if (
            location.region &&
            registration.location.region !== location.region
          )
            continue;
        }

        discoveredContacts.push({
          deviceId: registration.deviceId,
          deviceName: registration.deviceName,
          publicKey: registration.publicKey,
          lastSeen: registration.lastSeen,
          location: registration.location,
        });

        if (discoveredContacts.length >= maxResults) break;
      }

      return new Response(
        JSON.stringify({
          contacts: discoveredContacts,
          timestamp: now,
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    } catch (error) {
      console.error('Contact discovery error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  }

  private async handleContactRequest(
    request: Request,
    corsHeaders: Record<string, string>
  ): Promise<Response> {
    try {
      const body = (await request.json()) as any;
      const { fromDeviceId, toDeviceId, message } = body;

      if (!fromDeviceId || !toDeviceId) {
        return new Response(
          JSON.stringify({ error: 'Missing required fields' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Rate limiting
      if (!this.checkRateLimit(`request:${fromDeviceId}`, 10, 3600000)) {
        // 10 requests per hour
        return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), {
          status: 429,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      const requestId = crypto.randomUUID();
      const now = Date.now();
      const expiresAt = now + 24 * 60 * 60 * 1000; // 24 hours

      const contactRequest: ContactRequest = {
        id: requestId,
        fromDeviceId,
        toDeviceId,
        message,
        timestamp: now,
        status: 'pending',
        expiresAt,
      };

      this.contactRequests.set(requestId, contactRequest);
      await this.persistData();

      return new Response(JSON.stringify({ success: true, requestId }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Contact request error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  }

  private async handleContactResponse(
    request: Request,
    corsHeaders: Record<string, string>
  ): Promise<Response> {
    try {
      const body = (await request.json()) as any;
      const { requestId, accepted, deviceId } = body;

      if (!requestId || accepted === undefined || !deviceId) {
        return new Response(
          JSON.stringify({ error: 'Missing required fields' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      const contactRequest = this.contactRequests.get(requestId);
      if (!contactRequest) {
        return new Response(
          JSON.stringify({ error: 'Contact request not found' }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      // Verify the responding device is the target
      if (contactRequest.toDeviceId !== deviceId) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Check if request has expired
      if (Date.now() > contactRequest.expiresAt) {
        contactRequest.status = 'expired';
        await this.persistData();
        return new Response(
          JSON.stringify({ error: 'Contact request has expired' }),
          {
            status: 410,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      contactRequest.status = accepted ? 'accepted' : 'rejected';
      await this.persistData();

      return new Response(
        JSON.stringify({ success: true, status: contactRequest.status }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    } catch (error) {
      console.error('Contact response error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  }

  private async handlePresenceUpdateHTTP(
    request: Request,
    corsHeaders: Record<string, string>
  ): Promise<Response> {
    try {
      const body = (await request.json()) as any;
      const { deviceId, isOnline = true } = body;

      if (!deviceId) {
        return new Response(JSON.stringify({ error: 'Device ID required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      const registration = this.deviceRegistrations.get(deviceId);
      if (!registration) {
        return new Response(
          JSON.stringify({ error: 'Device not registered' }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }

      registration.lastSeen = Date.now();
      registration.isOnline = isOnline;
      await this.persistData();

      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Presence update error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  }
}
