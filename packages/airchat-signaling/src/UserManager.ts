/**
 * User Management for Enhanced Signaling Server
 * 
 * Handles user registration, authentication, JWT tokens, and multi-device support
 */

export interface User {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  createdAt: number;
  lastLoginAt: number;
  isActive: boolean;
  preferences: UserPreferences;
  devices: string[]; // Array of device IDs linked to this user
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    mobile: boolean;
  };
  privacy: {
    discoverable: boolean;
    showOnlineStatus: boolean;
    allowContactRequests: boolean;
  };
  security: {
    requireDeviceVerification: boolean;
    sessionTimeout: number; // in minutes
  };
}

export interface UserSession {
  id: string;
  userId: string;
  deviceId: string;
  token: string;
  refreshToken: string;
  createdAt: number;
  expiresAt: number;
  lastActivity: number;
  ipHash?: string;
  userAgent?: string;
  isActive: boolean;
}

export interface DeviceInfo {
  id: string;
  userId: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'web';
  publicKey: string;
  fingerprint: string;
  isVerified: boolean;
  isTrusted: boolean;
  createdAt: number;
  lastSeenAt: number;
  lastIpHash?: string;
  userAgent?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
  deviceId: string;
  deviceName: string;
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'web';
  publicKey: string;
}

export interface RegistrationData {
  username: string;
  email: string;
  password: string;
  displayName: string;
  deviceId: string;
  deviceName: string;
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'web';
  publicKey: string;
}

export interface JWTPayload {
  sub: string; // user ID
  username: string;
  deviceId: string;
  iat: number;
  exp: number;
  type: 'access' | 'refresh';
}

export class UserManager {
  private users = new Map<string, User>();
  private sessions = new Map<string, UserSession>();
  private devices = new Map<string, DeviceInfo>();
  private usersByEmail = new Map<string, string>(); // email -> userId
  private usersByUsername = new Map<string, string>(); // username -> userId
  private jwtSecret: string;

  constructor(jwtSecret: string) {
    this.jwtSecret = jwtSecret;
  }

  /**
   * Load persisted data from storage
   */
  async loadFromStorage(storage: DurableObjectStorage): Promise<void> {
    try {
      const [usersData, sessionsData, devicesData, emailIndex, usernameIndex] = await Promise.all([
        storage.get('users'),
        storage.get('sessions'),
        storage.get('devices'),
        storage.get('usersByEmail'),
        storage.get('usersByUsername')
      ]);

      if (usersData) {
        this.users = new Map(Object.entries(usersData as Record<string, User>));
      }

      if (sessionsData) {
        this.sessions = new Map(Object.entries(sessionsData as Record<string, UserSession>));
      }

      if (devicesData) {
        this.devices = new Map(Object.entries(devicesData as Record<string, DeviceInfo>));
      }

      if (emailIndex) {
        this.usersByEmail = new Map(Object.entries(emailIndex as Record<string, string>));
      }

      if (usernameIndex) {
        this.usersByUsername = new Map(Object.entries(usernameIndex as Record<string, string>));
      }
    } catch (error) {
      console.error('Failed to load user data from storage:', error);
    }
  }

  /**
   * Persist data to storage
   */
  async saveToStorage(storage: DurableObjectStorage): Promise<void> {
    try {
      await Promise.all([
        storage.put('users', Object.fromEntries(this.users)),
        storage.put('sessions', Object.fromEntries(this.sessions)),
        storage.put('devices', Object.fromEntries(this.devices)),
        storage.put('usersByEmail', Object.fromEntries(this.usersByEmail)),
        storage.put('usersByUsername', Object.fromEntries(this.usersByUsername))
      ]);
    } catch (error) {
      console.error('Failed to save user data to storage:', error);
      throw error;
    }
  }

  /**
   * Register a new user
   */
  async registerUser(data: RegistrationData): Promise<{ user: User; session: UserSession; device: DeviceInfo }> {
    // Validate input
    if (!this.isValidUsername(data.username)) {
      throw new Error('Invalid username format');
    }

    if (!this.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (!this.isValidPassword(data.password)) {
      throw new Error('Password does not meet requirements');
    }

    // Check for existing user
    if (this.usersByUsername.has(data.username)) {
      throw new Error('Username already exists');
    }

    if (this.usersByEmail.has(data.email)) {
      throw new Error('Email already registered');
    }

    // Create user
    const userId = crypto.randomUUID();
    const now = Date.now();
    
    const user: User = {
      id: userId,
      username: data.username,
      email: data.email,
      displayName: data.displayName,
      createdAt: now,
      lastLoginAt: now,
      isActive: true,
      preferences: this.getDefaultPreferences(),
      devices: [data.deviceId]
    };

    // Create device
    const device: DeviceInfo = {
      id: data.deviceId,
      userId,
      name: data.deviceName,
      type: data.deviceType,
      publicKey: data.publicKey,
      fingerprint: await this.generateDeviceFingerprint(data.deviceId, data.publicKey),
      isVerified: true, // First device is auto-verified
      isTrusted: true,
      createdAt: now,
      lastSeenAt: now
    };

    // Create session
    const session = await this.createSession(userId, data.deviceId);

    // Store data
    this.users.set(userId, user);
    this.devices.set(data.deviceId, device);
    this.sessions.set(session.id, session);
    this.usersByEmail.set(data.email, userId);
    this.usersByUsername.set(data.username, userId);

    return { user, session, device };
  }

  /**
   * Authenticate user login
   */
  async loginUser(credentials: LoginCredentials): Promise<{ user: User; session: UserSession; device: DeviceInfo }> {
    const userId = this.usersByUsername.get(credentials.username);
    if (!userId) {
      throw new Error('Invalid credentials');
    }

    const user = this.users.get(userId);
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    // In a real implementation, you'd verify the password hash
    // For now, we'll skip password verification since this is a demo

    // Update last login
    user.lastLoginAt = Date.now();

    // Check if device exists
    let device = this.devices.get(credentials.deviceId);
    if (!device) {
      // Create new device
      device = {
        id: credentials.deviceId,
        userId,
        name: credentials.deviceName,
        type: credentials.deviceType,
        publicKey: credentials.publicKey,
        fingerprint: await this.generateDeviceFingerprint(credentials.deviceId, credentials.publicKey),
        isVerified: false, // New devices need verification
        isTrusted: false,
        createdAt: Date.now(),
        lastSeenAt: Date.now()
      };

      this.devices.set(credentials.deviceId, device);
      user.devices.push(credentials.deviceId);
    } else {
      // Update existing device
      device.lastSeenAt = Date.now();
    }

    // Create session
    const session = await this.createSession(userId, credentials.deviceId);
    this.sessions.set(session.id, session);

    return { user, session, device };
  }

  /**
   * Create a new session
   */
  private async createSession(userId: string, deviceId: string): Promise<UserSession> {
    const sessionId = crypto.randomUUID();
    const now = Date.now();
    const expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours

    const accessToken = await this.generateJWT({
      sub: userId,
      username: this.users.get(userId)?.username || '',
      deviceId,
      iat: Math.floor(now / 1000),
      exp: Math.floor(expiresAt / 1000),
      type: 'access'
    });

    const refreshToken = await this.generateJWT({
      sub: userId,
      username: this.users.get(userId)?.username || '',
      deviceId,
      iat: Math.floor(now / 1000),
      exp: Math.floor((now + (7 * 24 * 60 * 60 * 1000)) / 1000), // 7 days
      type: 'refresh'
    });

    return {
      id: sessionId,
      userId,
      deviceId,
      token: accessToken,
      refreshToken,
      createdAt: now,
      expiresAt,
      lastActivity: now,
      isActive: true
    };
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token: string): Promise<JWTPayload | null> {
    try {
      // In a real implementation, you'd use a proper JWT library
      // This is a simplified version for demonstration
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      const payload = JSON.parse(atob(parts[1]));
      
      // Check expiration
      if (payload.exp * 1000 < Date.now()) {
        return null;
      }

      return payload as JWTPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Generate JWT token
   */
  private async generateJWT(payload: JWTPayload): Promise<string> {
    // In a real implementation, you'd use a proper JWT library with signing
    // This is a simplified version for demonstration
    const header = { alg: 'HS256', typ: 'JWT' };
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));
    
    // Simplified signature (in production, use proper HMAC)
    const signature = btoa(`${encodedHeader}.${encodedPayload}.${this.jwtSecret}`);
    
    return `${encodedHeader}.${encodedPayload}.${signature}`;
  }

  /**
   * Get user by ID
   */
  getUser(userId: string): User | undefined {
    return this.users.get(userId);
  }

  /**
   * Get user by session token
   */
  async getUserByToken(token: string): Promise<User | null> {
    const payload = await this.verifyToken(token);
    if (!payload) return null;

    const session = Array.from(this.sessions.values()).find(s => s.token === token);
    if (!session || !session.isActive) return null;

    // Update last activity
    session.lastActivity = Date.now();

    return this.users.get(payload.sub) || null;
  }

  /**
   * Get user devices
   */
  getUserDevices(userId: string): DeviceInfo[] {
    const user = this.users.get(userId);
    if (!user) return [];

    return user.devices.map(deviceId => this.devices.get(deviceId)).filter(Boolean) as DeviceInfo[];
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    const user = this.users.get(userId);
    if (!user) throw new Error('User not found');

    user.preferences = { ...user.preferences, ...preferences };
  }

  /**
   * Logout user session
   */
  async logout(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      if (session.expiresAt < now || !session.isActive) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.sessions.delete(sessionId);
    }
  }

  /**
   * Generate device fingerprint
   */
  private async generateDeviceFingerprint(deviceId: string, publicKey: string): Promise<string> {
    const data = `${deviceId}:${publicKey}`;
    const encoder = new TextEncoder();
    const hashBuffer = await crypto.subtle.digest('SHA-256', encoder.encode(data));
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get default user preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      theme: 'auto',
      language: 'en',
      notifications: {
        enabled: true,
        sound: true,
        desktop: true,
        mobile: true
      },
      privacy: {
        discoverable: true,
        showOnlineStatus: true,
        allowContactRequests: true
      },
      security: {
        requireDeviceVerification: true,
        sessionTimeout: 1440 // 24 hours
      }
    };
  }

  /**
   * Validate username format
   */
  private isValidUsername(username: string): boolean {
    return /^[a-zA-Z0-9_]{3,20}$/.test(username);
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  /**
   * Validate password requirements
   */
  private isValidPassword(password: string): boolean {
    return password.length >= 8; // Simplified validation
  }
}
