// Re-export all types
export * from './signaling';
export * from './messages';

// Common utility types
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet';
  lastSeen: number;
  publicKey: string;
}

export interface Contact {
  id: string;
  name: string;
  avatar?: string;
  publicKey: string;
  devices: DeviceInfo[];
  isBlocked: boolean;
  addedAt: number;
  lastSeen?: number;
  status: 'online' | 'offline' | 'away';
}

export interface FileTransfer {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  from: string;
  to: string;
  status: 'pending' | 'transferring' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-1
  speed: number; // bytes per second
  startedAt: number;
  completedAt?: number;
  chunks: FileChunk[];
}

export interface FileChunk {
  index: number;
  size: number;
  checksum: string;
  encrypted: boolean;
  transferred: boolean;
}

export interface AppConfig {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
  };
  privacy: {
    readReceipts: boolean;
    onlineStatus: boolean;
    lastSeen: boolean;
  };
  security: {
    autoLockTimeout: number; // minutes
    requireAuth: boolean;
  };
}

// Event types for the application
export interface AppEvent {
  type: string;
  timestamp: number;
  data?: unknown;
}

export interface ConnectionEvent extends AppEvent {
  type: 'connection' | 'disconnection' | 'reconnection';
  peerId: string;
  data: {
    state: string;
    reason?: string;
  };
}

export interface MessageEvent extends AppEvent {
  type: 'message-received' | 'message-sent' | 'message-delivered' | 'message-read';
  data: {
    messageId: string;
    conversationId: string;
    from?: string;
    to?: string;
  };
}

export interface FileTransferEvent extends AppEvent {
  type: 'transfer-started' | 'transfer-progress' | 'transfer-completed' | 'transfer-failed';
  data: {
    transferId: string;
    progress?: number;
    error?: string;
  };
}
