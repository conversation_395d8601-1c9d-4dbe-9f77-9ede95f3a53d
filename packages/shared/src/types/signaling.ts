import { z } from 'zod';

// Base message schema
export const SignalingMessageSchema = z.object({
  type: z.string(),
  from: z.string(),
  to: z.string().optional(),
  room: z.string().optional(),
  timestamp: z.number(),
});

// WebRTC signaling messages
export const OfferMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('offer'),
  offer: z.object({
    type: z.literal('offer'),
    sdp: z.string(),
  }),
});

export const AnswerMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('answer'),
  answer: z.object({
    type: z.literal('answer'),
    sdp: z.string(),
  }),
});

export const IceCandidateMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('ice-candidate'),
  candidate: z.object({
    candidate: z.string(),
    sdpMLineIndex: z.number().nullable(),
    sdpMid: z.string().nullable(),
  }),
});

// Room management messages
export const JoinRoomMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('join-room'),
  room: z.string(),
});

export const LeaveRoomMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('leave-room'),
  room: z.string(),
});

export const PeerJoinedMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('peer-joined'),
  peerId: z.string(),
  room: z.string(),
});

export const PeerLeftMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('peer-left'),
  peerId: z.string(),
  room: z.string(),
});

// Presence messages
export const PresenceUpdateMessageSchema = SignalingMessageSchema.extend({
  type: z.literal('presence-update'),
  status: z.enum(['online', 'offline', 'away']),
  lastSeen: z.number().optional(),
});

// Union type for all signaling messages
export const AnySignalingMessageSchema = z.discriminatedUnion('type', [
  OfferMessageSchema,
  AnswerMessageSchema,
  IceCandidateMessageSchema,
  JoinRoomMessageSchema,
  LeaveRoomMessageSchema,
  PeerJoinedMessageSchema,
  PeerLeftMessageSchema,
  PresenceUpdateMessageSchema,
]);

// TypeScript types
export type SignalingMessage = z.infer<typeof SignalingMessageSchema>;
export type OfferMessage = z.infer<typeof OfferMessageSchema>;
export type AnswerMessage = z.infer<typeof AnswerMessageSchema>;
export type IceCandidateMessage = z.infer<typeof IceCandidateMessageSchema>;
export type JoinRoomMessage = z.infer<typeof JoinRoomMessageSchema>;
export type LeaveRoomMessage = z.infer<typeof LeaveRoomMessageSchema>;
export type PeerJoinedMessage = z.infer<typeof PeerJoinedMessageSchema>;
export type PeerLeftMessage = z.infer<typeof PeerLeftMessageSchema>;
export type PresenceUpdateMessage = z.infer<typeof PresenceUpdateMessageSchema>;
export type AnySignalingMessage = z.infer<typeof AnySignalingMessageSchema>;

// Connection state types
export type ConnectionState = 'connecting' | 'connected' | 'disconnected' | 'failed' | 'closed';
export type IceConnectionState = 'new' | 'checking' | 'connected' | 'completed' | 'failed' | 'disconnected' | 'closed';

// Room and peer management
export interface Room {
  id: string;
  participants: Set<string>;
  createdAt: number;
  lastActivity: number;
}

export interface Peer {
  id: string;
  status: 'online' | 'offline' | 'away';
  lastSeen: number;
  rooms: Set<string>;
}

// Error types
export interface SignalingError {
  code: string;
  message: string;
  details?: unknown;
}
