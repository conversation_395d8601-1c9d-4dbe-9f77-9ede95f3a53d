import { z } from 'zod';

// Message content types
export const TextMessageContentSchema = z.object({
  type: z.literal('text'),
  text: z.string(),
});

export const FileMessageContentSchema = z.object({
  type: z.literal('file'),
  fileName: z.string(),
  fileSize: z.number(),
  fileType: z.string(),
  transferId: z.string(),
  chunks: z.number(),
});

export const ImageMessageContentSchema = z.object({
  type: z.literal('image'),
  fileName: z.string(),
  fileSize: z.number(),
  width: z.number(),
  height: z.number(),
  transferId: z.string(),
  thumbnail: z.string().optional(), // base64 encoded thumbnail
});

export const VoiceMessageContentSchema = z.object({
  type: z.literal('voice'),
  duration: z.number(), // in seconds
  transferId: z.string(),
  waveform: z.array(z.number()).optional(), // audio waveform data
});

// Union type for message content
export const MessageContentSchema = z.discriminatedUnion('type', [
  TextMessageContentSchema,
  FileMessageContentSchema,
  ImageMessageContentSchema,
  VoiceMessageContentSchema,
]);

// Core message structure
export const MessageSchema = z.object({
  id: z.string(),
  from: z.string(),
  to: z.string(),
  content: MessageContentSchema,
  timestamp: z.number(),
  edited: z.boolean().default(false),
  editedAt: z.number().optional(),
  replyTo: z.string().optional(), // ID of message being replied to
  reactions: z.record(z.string(), z.array(z.string())).default({}), // emoji -> array of user IDs
});

// Message status for delivery tracking
export const MessageStatusSchema = z.enum(['sending', 'sent', 'delivered', 'read', 'failed']);

// Encrypted message envelope
export const EncryptedMessageSchema = z.object({
  id: z.string(),
  from: z.string(),
  to: z.string(),
  encryptedContent: z.string(), // base64 encoded encrypted content
  iv: z.string(), // base64 encoded initialization vector
  timestamp: z.number(),
  keyFingerprint: z.string(), // fingerprint of the key used for encryption
});

// Group message structure
export const GroupMessageSchema = MessageSchema.extend({
  groupId: z.string(),
  to: z.string(), // group ID
});

// Message delivery confirmation
export const MessageDeliverySchema = z.object({
  messageId: z.string(),
  status: MessageStatusSchema,
  timestamp: z.number(),
  from: z.string(),
});

// Typing indicator
export const TypingIndicatorSchema = z.object({
  from: z.string(),
  to: z.string(),
  isTyping: z.boolean(),
  timestamp: z.number(),
});

// Message reaction
export const MessageReactionSchema = z.object({
  messageId: z.string(),
  emoji: z.string(),
  from: z.string(),
  action: z.enum(['add', 'remove']),
  timestamp: z.number(),
});

// TypeScript types
export type TextMessageContent = z.infer<typeof TextMessageContentSchema>;
export type FileMessageContent = z.infer<typeof FileMessageContentSchema>;
export type ImageMessageContent = z.infer<typeof ImageMessageContentSchema>;
export type VoiceMessageContent = z.infer<typeof VoiceMessageContentSchema>;
export type MessageContent = z.infer<typeof MessageContentSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type MessageStatus = z.infer<typeof MessageStatusSchema>;
export type EncryptedMessage = z.infer<typeof EncryptedMessageSchema>;
export type GroupMessage = z.infer<typeof GroupMessageSchema>;
export type MessageDelivery = z.infer<typeof MessageDeliverySchema>;
export type TypingIndicator = z.infer<typeof TypingIndicatorSchema>;
export type MessageReaction = z.infer<typeof MessageReactionSchema>;

// Conversation types
export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  participants: string[];
  lastMessage?: Message;
  lastActivity: number;
  unreadCount: number;
  name?: string; // for group conversations
  avatar?: string; // for group conversations
}

// Message search result
export interface MessageSearchResult {
  message: Message;
  conversation: Conversation;
  snippet: string; // highlighted text snippet
  score: number; // relevance score
}
